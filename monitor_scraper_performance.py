#!/usr/bin/env python3
"""
📊 REAL-TIME SCRAPER PERFORMANCE MONITOR
=======================================

Monitor scraper performance in real-time and provide recommendations.
"""

import time
import re
import json
from datetime import datetime
from collections import defaultdict, deque
from pathlib import Path

class RealTimeScraperMonitor:
    """Real-time monitoring of scraper performance"""
    
    def __init__(self, log_file_pattern="scraper_test_*.log"):
        self.log_pattern = log_file_pattern
        self.stats = {
            'blocking_patterns': defaultdict(int),
            'success_patterns': defaultdict(int),
            'strategy_effectiveness': defaultdict(lambda: {'attempts': 0, 'successes': 0}),
            'sportsbook_performance': defaultdict(lambda: {'attempts': 0, 'successes': 0, 'blocks': 0}),
            'timing_analysis': deque(maxlen=100),
            'error_patterns': defaultdict(int),
            'recommendations': []
        }
        self.last_position = 0
        
    def analyze_log_line(self, line: str, timestamp: str):
        """Analyze a single log line for patterns"""
        
        # Blocking detection patterns
        blocking_indicators = [
            'blocked', 'captcha', 'access denied', 'rate limit', 
            'suspicious activity', 'verify you are human', 'cloudflare'
        ]
        
        for indicator in blocking_indicators:
            if indicator.lower() in line.lower():
                self.stats['blocking_patterns'][indicator] += 1
                
                # Extract sportsbook if mentioned
                sportsbooks = ['draftkings', 'fanduel', 'betmgm', 'bet365', 'caesars']
                for sb in sportsbooks:
                    if sb in line.lower():
                        self.stats['sportsbook_performance'][sb]['blocks'] += 1
                        break
        
        # Success patterns
        if 'props found' in line.lower() or 'successful' in line.lower():
            # Extract number of props
            prop_match = re.search(r'(\d+)\s+props?', line)
            if prop_match:
                prop_count = int(prop_match.group(1))
                self.stats['success_patterns']['props_found'] += prop_count
                
                # Extract sportsbook
                sportsbooks = ['draftkings', 'fanduel', 'betmgm', 'bet365', 'caesars']
                for sb in sportsbooks:
                    if sb in line.lower():
                        self.stats['sportsbook_performance'][sb]['successes'] += 1
                        break
        
        # Strategy analysis
        strategies = ['chrome', 'firefox', 'requests', 'proxy', 'selenium']
        for strategy in strategies:
            if strategy in line.lower():
                self.stats['strategy_effectiveness'][strategy]['attempts'] += 1
                if 'success' in line.lower() or 'created' in line.lower():
                    self.stats['strategy_effectiveness'][strategy]['successes'] += 1
        
        # Error patterns
        if 'error' in line.lower() or 'failed' in line.lower():
            # Extract error type
            error_patterns = [
                'timeout', 'connection', 'selenium', 'driver', 'proxy', 
                'captcha', 'blocked', 'rate limit'
            ]
            for pattern in error_patterns:
                if pattern in line.lower():
                    self.stats['error_patterns'][pattern] += 1
        
        # Timing analysis
        if 'scraping' in line.lower() and any(sb in line.lower() for sb in ['draftkings', 'fanduel']):
            self.stats['timing_analysis'].append({
                'timestamp': timestamp,
                'action': 'scrape_start',
                'line': line[:100]
            })
    
    def monitor_log_file(self, log_file: Path):
        """Monitor a specific log file for new entries"""
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                f.seek(self.last_position)
                new_lines = f.readlines()
                self.last_position = f.tell()
                
                for line in new_lines:
                    # Extract timestamp
                    timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    timestamp = timestamp_match.group(1) if timestamp_match else datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    
                    self.analyze_log_line(line.strip(), timestamp)
                    
        except FileNotFoundError:
            pass
        except Exception as e:
            print(f"Error monitoring log: {e}")
    
    def generate_recommendations(self):
        """Generate recommendations based on current stats"""
        recommendations = []
        
        # Blocking analysis
        total_blocks = sum(self.stats['blocking_patterns'].values())
        if total_blocks > 10:
            recommendations.append("🚫 High blocking detected - consider increasing delays")
            
            top_block_reason = max(self.stats['blocking_patterns'].items(), key=lambda x: x[1])[0]
            recommendations.append(f"🎯 Main blocking reason: {top_block_reason}")
        
        # Strategy effectiveness
        for strategy, data in self.stats['strategy_effectiveness'].items():
            if data['attempts'] > 0:
                success_rate = (data['successes'] / data['attempts']) * 100
                if success_rate > 80:
                    recommendations.append(f"✅ {strategy.title()} strategy very effective ({success_rate:.1f}%)")
                elif success_rate < 20:
                    recommendations.append(f"❌ {strategy.title()} strategy failing ({success_rate:.1f}%)")
        
        # Sportsbook performance
        for sportsbook, data in self.stats['sportsbook_performance'].items():
            if data['blocks'] > data['successes'] * 2:
                recommendations.append(f"🏢 {sportsbook.title()} heavily blocking - try different approach")
        
        # Error patterns
        if self.stats['error_patterns']['timeout'] > 5:
            recommendations.append("⏱️ Many timeouts - increase wait times")
        
        if self.stats['error_patterns']['proxy'] > 3:
            recommendations.append("🔄 Proxy issues - check proxy list")
        
        self.stats['recommendations'] = recommendations
        return recommendations
    
    def print_real_time_stats(self):
        """Print current statistics"""
        print("\n" + "="*50)
        print("📊 REAL-TIME SCRAPER MONITORING")
        print("="*50)
        print(f"🕐 Last Update: {datetime.now().strftime('%H:%M:%S')}")
        
        # Blocking summary
        total_blocks = sum(self.stats['blocking_patterns'].values())
        print(f"\n🚫 BLOCKING ANALYSIS:")
        print(f"   Total blocks detected: {total_blocks}")
        for reason, count in sorted(self.stats['blocking_patterns'].items(), key=lambda x: x[1], reverse=True)[:3]:
            print(f"   • {reason}: {count} times")
        
        # Success summary
        total_props = self.stats['success_patterns']['props_found']
        print(f"\n✅ SUCCESS ANALYSIS:")
        print(f"   Total props found: {total_props}")
        
        # Strategy effectiveness
        print(f"\n🛠️ STRATEGY EFFECTIVENESS:")
        for strategy, data in self.stats['strategy_effectiveness'].items():
            if data['attempts'] > 0:
                success_rate = (data['successes'] / data['attempts']) * 100
                print(f"   • {strategy.title()}: {data['successes']}/{data['attempts']} ({success_rate:.1f}%)")
        
        # Sportsbook performance
        print(f"\n🏢 SPORTSBOOK PERFORMANCE:")
        for sportsbook, data in self.stats['sportsbook_performance'].items():
            if data['attempts'] > 0 or data['blocks'] > 0:
                print(f"   • {sportsbook.title()}: {data['successes']} success, {data['blocks']} blocks")
        
        # Current recommendations
        recommendations = self.generate_recommendations()
        if recommendations:
            print(f"\n💡 CURRENT RECOMMENDATIONS:")
            for rec in recommendations[-5:]:  # Show last 5
                print(f"   {rec}")
        
        print("-" * 50)
    
    def save_analysis_report(self):
        """Save detailed analysis to file"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'blocking_patterns': dict(self.stats['blocking_patterns']),
            'success_patterns': dict(self.stats['success_patterns']),
            'strategy_effectiveness': {k: dict(v) for k, v in self.stats['strategy_effectiveness'].items()},
            'sportsbook_performance': {k: dict(v) for k, v in self.stats['sportsbook_performance'].items()},
            'error_patterns': dict(self.stats['error_patterns']),
            'recommendations': self.stats['recommendations']
        }
        
        filename = f"scraper_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📄 Analysis report saved: {filename}")

def main():
    """Main monitoring loop"""
    monitor = RealTimeScraperMonitor()
    
    print("📊 Starting Real-Time Scraper Monitor")
    print("Monitoring for log files matching pattern: scraper_test_*.log")
    print("Press Ctrl+C to stop monitoring")
    
    try:
        while True:
            # Find latest log file
            log_files = list(Path('.').glob('scraper_test_*.log'))
            if log_files:
                latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
                monitor.monitor_log_file(latest_log)
                monitor.print_real_time_stats()
            else:
                print("⏳ Waiting for log files...")
            
            time.sleep(5)  # Update every 5 seconds
            
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped")
        monitor.save_analysis_report()

if __name__ == "__main__":
    main()
