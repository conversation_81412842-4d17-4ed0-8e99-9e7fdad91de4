#!/usr/bin/env python3
"""
🔗 LIVE DATA BRIDGE
==================

Real-time data bridge between Consolidated Military-Grade Live System 
and Enhanced Unified Dashboard Server.

This bridge:
- Connects to the military-grade scraper's database
- Fetches real live props, odds, and game data
- Serves it to the dashboard via API endpoints
- Updates in real-time (no mock data)

Author: WNBA Analytics Team
"""

import sqlite3
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from flask import Flask, jsonify
from flask_cors import CORS
import threading

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LiveDataBridge:
    """Bridge between military-grade scraper and dashboard"""
    
    def __init__(self, port=5001):
        self.port = port
        self.app = Flask(__name__)
        CORS(self.app)
        
        # Database paths from military-grade system
        self.military_db = "military_grade_wnba_data.db"
        self.odds_db = "expert_odds_cache.db"
        
        # Cache for real-time data
        self.live_props_cache = {}
        self.live_games_cache = {}
        self.live_odds_cache = {}
        self.live_player_stats_cache = {}
        
        # Setup routes
        self._setup_routes()
        
        # Start background data fetcher
        self.running = True
        self.data_thread = threading.Thread(target=self._continuous_data_fetch)
        self.data_thread.daemon = True
        self.data_thread.start()
        
        logger.info("🔗 Live Data Bridge initialized")
    
    def _setup_routes(self):
        """Setup API routes for real live data"""
        
        @self.app.route('/api/live/props')
        def get_live_props():
            """Get real live player props from military scraper"""
            return jsonify(self._get_real_live_props())
        
        @self.app.route('/api/live/games')
        def get_live_games():
            """Get real live games data"""
            return jsonify(self._get_real_live_games())
        
        @self.app.route('/api/live/odds')
        def get_live_odds():
            """Get real live odds (H2H, spreads, totals)"""
            return jsonify(self._get_real_live_odds())
        
        @self.app.route('/api/live/player_stats')
        def get_live_player_stats():
            """Get real live player statistics"""
            return jsonify(self._get_real_live_player_stats())
        
        @self.app.route('/api/live/play_by_play')
        def get_live_play_by_play():
            """Get real live play-by-play events"""
            return jsonify(self._get_real_play_by_play())
        
        @self.app.route('/api/status')
        def get_bridge_status():
            """Get bridge status and data counts"""
            return jsonify({
                'status': 'active',
                'timestamp': datetime.now().isoformat(),
                'data_counts': {
                    'live_props': len(self.live_props_cache),
                    'live_games': len(self.live_games_cache),
                    'live_odds': len(self.live_odds_cache),
                    'live_player_stats': len(self.live_player_stats_cache)
                },
                'last_update': datetime.now().isoformat()
            })
    
    def _continuous_data_fetch(self):
        """Continuously fetch real data from military-grade system"""
        while self.running:
            try:
                # Fetch real live props
                self.live_props_cache = self._fetch_real_props_from_db()
                
                # Fetch real live games
                self.live_games_cache = self._fetch_real_games_from_db()
                
                # Fetch real live odds
                self.live_odds_cache = self._fetch_real_odds_from_db()
                
                # Fetch real live player stats
                self.live_player_stats_cache = self._fetch_real_player_stats_from_db()
                
                logger.info(f"🔄 Data updated: {len(self.live_props_cache)} props, {len(self.live_games_cache)} games, {len(self.live_odds_cache)} odds")
                
                # Update every 10 seconds
                time.sleep(10)
                
            except Exception as e:
                logger.error(f"❌ Data fetch error: {e}")
                time.sleep(30)  # Wait longer on error
    
    def _fetch_real_props_from_db(self) -> Dict[str, Any]:
        """Fetch real player props from military-grade database"""
        try:
            with sqlite3.connect(self.military_db) as conn:
                cursor = conn.cursor()
                
                # Get recent props (last 2 hours)
                cutoff_time = datetime.now() - timedelta(hours=2)
                
                cursor.execute("""
                    SELECT player_name, prop_type, line, over_odds, under_odds, 
                           sportsbook, game_id, confidence_score, market_movement,
                           last_updated, data_quality_score
                    FROM player_props 
                    WHERE last_updated > ?
                    ORDER BY last_updated DESC
                """, (cutoff_time.isoformat(),))
                
                props = []
                for row in cursor.fetchall():
                    props.append({
                        'player_name': row[0],
                        'prop_type': row[1],
                        'line': row[2],
                        'over_odds': row[3],
                        'under_odds': row[4],
                        'sportsbook': row[5],
                        'game_id': row[6],
                        'confidence_score': row[7],
                        'market_movement': row[8],
                        'last_updated': row[9],
                        'data_quality_score': row[10]
                    })
                
                return {'props': props, 'count': len(props)}
                
        except Exception as e:
            logger.error(f"❌ Props fetch error: {e}")
            return {'props': [], 'count': 0}
    
    def _fetch_real_games_from_db(self) -> Dict[str, Any]:
        """Fetch real live games from military-grade database"""
        try:
            with sqlite3.connect(self.military_db) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT game_id, home_team, away_team, home_score, away_score,
                           quarter, time_remaining, status, start_time
                    FROM live_games 
                    WHERE status IN ('live', 'scheduled')
                    ORDER BY start_time ASC
                """)
                
                games = []
                for row in cursor.fetchall():
                    games.append({
                        'game_id': row[0],
                        'home_team': row[1],
                        'away_team': row[2],
                        'home_score': row[3],
                        'away_score': row[4],
                        'quarter': row[5],
                        'time_remaining': row[6],
                        'status': row[7],
                        'start_time': row[8]
                    })
                
                return {'games': games, 'count': len(games)}
                
        except Exception as e:
            logger.error(f"❌ Games fetch error: {e}")
            return {'games': [], 'count': 0}
    
    def _fetch_real_odds_from_db(self) -> Dict[str, Any]:
        """Fetch real odds from expert odds database"""
        try:
            with sqlite3.connect(self.odds_db) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT home_team, away_team, odds_data, market_type, last_updated
                    FROM fresh_odds 
                    WHERE last_updated > datetime('now', '-2 hours')
                    ORDER BY last_updated DESC
                """)
                
                odds = []
                for row in cursor.fetchall():
                    try:
                        odds_data = json.loads(row[2])
                        odds.append({
                            'home_team': row[0],
                            'away_team': row[1],
                            'odds_data': odds_data,
                            'market_type': row[3],
                            'last_updated': row[4]
                        })
                    except:
                        continue
                
                return {'odds': odds, 'count': len(odds)}
                
        except Exception as e:
            logger.error(f"❌ Odds fetch error: {e}")
            return {'odds': [], 'count': 0}
    
    def _fetch_real_player_stats_from_db(self) -> Dict[str, Any]:
        """Fetch real live player stats from military-grade database"""
        try:
            with sqlite3.connect(self.military_db) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT player_name, team, game_id, minutes_played, points, rebounds,
                           assists, steals, blocks, turnovers, fouls, field_goals_made,
                           field_goals_attempted, three_pointers_made, three_pointers_attempted,
                           free_throws_made, free_throws_attempted, plus_minus, is_on_court,
                           last_updated
                    FROM live_player_stats 
                    WHERE last_updated > datetime('now', '-2 hours')
                    ORDER BY last_updated DESC
                """)
                
                stats = []
                for row in cursor.fetchall():
                    stats.append({
                        'player_name': row[0],
                        'team': row[1],
                        'game_id': row[2],
                        'minutes_played': row[3],
                        'points': row[4],
                        'rebounds': row[5],
                        'assists': row[6],
                        'steals': row[7],
                        'blocks': row[8],
                        'turnovers': row[9],
                        'fouls': row[10],
                        'field_goals_made': row[11],
                        'field_goals_attempted': row[12],
                        'three_pointers_made': row[13],
                        'three_pointers_attempted': row[14],
                        'free_throws_made': row[15],
                        'free_throws_attempted': row[16],
                        'plus_minus': row[17],
                        'is_on_court': row[18],
                        'last_updated': row[19]
                    })
                
                return {'player_stats': stats, 'count': len(stats)}
                
        except Exception as e:
            logger.error(f"❌ Player stats fetch error: {e}")
            return {'player_stats': [], 'count': 0}
    
    def _get_real_live_props(self):
        """Get cached real live props"""
        return self.live_props_cache
    
    def _get_real_live_games(self):
        """Get cached real live games"""
        return self.live_games_cache
    
    def _get_real_live_odds(self):
        """Get cached real live odds"""
        return self.live_odds_cache
    
    def _get_real_live_player_stats(self):
        """Get cached real live player stats"""
        return self.live_player_stats_cache
    
    def _get_real_play_by_play(self):
        """Get real play-by-play events"""
        try:
            with sqlite3.connect(self.military_db) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT event_id, game_id, quarter, time_remaining, event_type,
                           player_name, team, description, home_score, away_score,
                           shot_type, shot_made, assist_player, timestamp
                    FROM play_by_play 
                    WHERE timestamp > datetime('now', '-2 hours')
                    ORDER BY timestamp DESC
                    LIMIT 100
                """)
                
                events = []
                for row in cursor.fetchall():
                    events.append({
                        'event_id': row[0],
                        'game_id': row[1],
                        'quarter': row[2],
                        'time_remaining': row[3],
                        'event_type': row[4],
                        'player_name': row[5],
                        'team': row[6],
                        'description': row[7],
                        'home_score': row[8],
                        'away_score': row[9],
                        'shot_type': row[10],
                        'shot_made': row[11],
                        'assist_player': row[12],
                        'timestamp': row[13]
                    })
                
                return {'events': events, 'count': len(events)}
                
        except Exception as e:
            logger.error(f"❌ Play-by-play fetch error: {e}")
            return {'events': [], 'count': 0}
    
    def run(self):
        """Start the live data bridge server"""
        logger.info(f"🔗 Starting Live Data Bridge on port {self.port}")
        logger.info(f"🌐 Bridge API: http://127.0.0.1:{self.port}")
        logger.info("📊 Real-time data endpoints:")
        logger.info(f"   • Props: http://127.0.0.1:{self.port}/api/live/props")
        logger.info(f"   • Games: http://127.0.0.1:{self.port}/api/live/games")
        logger.info(f"   • Odds: http://127.0.0.1:{self.port}/api/live/odds")
        logger.info(f"   • Player Stats: http://127.0.0.1:{self.port}/api/live/player_stats")
        
        self.app.run(host='127.0.0.1', port=self.port, debug=False)

if __name__ == "__main__":
    bridge = LiveDataBridge()
    bridge.run()
