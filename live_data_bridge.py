#!/usr/bin/env python3
"""
🔗 LIVE DATA BRIDGE
==================

Real-time data bridge between Consolidated Military-Grade Live System 
and Enhanced Unified Dashboard Server.

This bridge:
- Connects to the military-grade scraper's database
- Fetches real live props, odds, and game data
- Serves it to the dashboard via API endpoints
- Updates in real-time (no mock data)

Author: WNBA Analytics Team
"""

import sqlite3
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from flask import Flask, jsonify
from flask_cors import CORS
import threading

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LiveDataBridge:
    """Bridge between military-grade scraper and dashboard"""
    
    def __init__(self, port=5001):
        self.port = port
        self.app = Flask(__name__)
        CORS(self.app)
        
        # Database paths from military-grade system
        self.military_db = "military_grade_wnba_data.db"
        self.odds_db = "expert_odds_cache.db"
        
        # Cache for real-time data
        self.live_props_cache = {}
        self.live_games_cache = {}
        self.live_odds_cache = {}
        self.live_player_stats_cache = {}
        
        # Setup routes
        self._setup_routes()
        
        # Start background data fetcher
        self.running = True
        self.data_thread = threading.Thread(target=self._continuous_data_fetch)
        self.data_thread.daemon = True
        self.data_thread.start()
        
        logger.info("🔗 Live Data Bridge initialized")
    
    def _setup_routes(self):
        """Setup API routes for real live data"""
        
        @self.app.route('/api/live/props')
        def get_live_props():
            """Get real live player props from military scraper"""
            return jsonify(self._get_real_live_props())
        
        @self.app.route('/api/live/games')
        def get_live_games():
            """Get real live games data"""
            return jsonify(self._get_real_live_games())
        
        @self.app.route('/api/live/odds')
        def get_live_odds():
            """Get real live odds (H2H, spreads, totals)"""
            return jsonify(self._get_real_live_odds())
        
        @self.app.route('/api/live/player_stats')
        def get_live_player_stats():
            """Get real live player statistics"""
            return jsonify(self._get_real_live_player_stats())
        
        @self.app.route('/api/live/play_by_play')
        def get_live_play_by_play():
            """Get real live play-by-play events"""
            return jsonify(self._get_real_play_by_play())
        
        @self.app.route('/api/status')
        def get_bridge_status():
            """Get bridge status and data counts"""
            return jsonify({
                'status': 'active',
                'timestamp': datetime.now().isoformat(),
                'data_counts': {
                    'live_props': len(self.live_props_cache),
                    'live_games': len(self.live_games_cache),
                    'live_odds': len(self.live_odds_cache),
                    'live_player_stats': len(self.live_player_stats_cache)
                },
                'last_update': datetime.now().isoformat()
            })
    
    def _continuous_data_fetch(self):
        """Continuously fetch real data from military-grade system"""
        while self.running:
            try:
                # Fetch real live props
                self.live_props_cache = self._fetch_real_props_from_db()
                
                # Fetch real live games
                self.live_games_cache = self._fetch_real_games_from_db()
                
                # Fetch real live odds
                self.live_odds_cache = self._fetch_real_odds_from_db()
                
                # Fetch real live player stats
                self.live_player_stats_cache = self._fetch_real_player_stats_from_db()
                
                logger.info(f"🔄 Data updated: {len(self.live_props_cache)} props, {len(self.live_games_cache)} games, {len(self.live_odds_cache)} odds")
                
                # Update every 10 seconds
                time.sleep(10)
                
            except Exception as e:
                logger.error(f"❌ Data fetch error: {e}")
                time.sleep(30)  # Wait longer on error
    
    def _fetch_real_props_from_db(self) -> Dict[str, Any]:
        """Fetch real player props from military-grade database"""
        try:
            with sqlite3.connect(self.military_db) as conn:
                cursor = conn.cursor()

                # First check what tables exist
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                logger.info(f"📋 Available tables: {tables}")

                # Try different possible table names
                props_tables = ['wnba_props', 'player_props', 'props', 'scraped_props', 'military_props']
                props_table = None

                for table in props_tables:
                    if table in tables:
                        props_table = table
                        break

                if not props_table:
                    logger.warning("⚠️ No props table found")
                    return {'props': [], 'count': 0}

                # Get table schema
                cursor.execute(f"PRAGMA table_info({props_table})")
                columns = [row[1] for row in cursor.fetchall()]
                logger.info(f"📋 {props_table} columns: {columns}")

                # Get recent props (last 24 hours for more data)
                cutoff_time = datetime.now() - timedelta(hours=24)

                # Get all props with proper column mapping
                cursor.execute(f"SELECT player_name, prop_type, line, over_odds, under_odds, sportsbook FROM {props_table} ORDER BY rowid DESC LIMIT 200")
                rows = cursor.fetchall()

                props = []
                for i, row in enumerate(rows):
                    # Skip empty player names
                    player_name = str(row[0]).strip() if row[0] else ''
                    if not player_name or player_name == 'None':
                        continue

                    props.append({
                        'id': i,
                        'player_name': player_name,
                        'prop_type': str(row[1]) if row[1] else 'points',
                        'line': float(row[2]) if row[2] else 0.0,
                        'over_odds': int(row[3]) if row[3] else -110,
                        'under_odds': int(row[4]) if row[4] else -110,
                        'sportsbook': str(row[5]) if row[5] else 'DraftKings',
                        'last_updated': datetime.now().isoformat()
                    })

                logger.info(f"📊 Found {len(props)} valid props from {len(rows)} total rows")
                return {'props': props, 'count': len(props)}

        except Exception as e:
            logger.error(f"❌ Props fetch error: {e}")
            return {'props': [], 'count': 0}
    
    def _fetch_real_games_from_db(self) -> Dict[str, Any]:
        """Fetch real live games from military-grade database"""
        try:
            with sqlite3.connect(self.military_db) as conn:
                cursor = conn.cursor()

                # Check what tables exist
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]

                # Try different possible table names
                games_tables = ['live_games', 'games', 'wnba_games', 'game_data']
                games_table = None

                for table in games_tables:
                    if table in tables:
                        games_table = table
                        break

                if not games_table:
                    logger.warning("⚠️ No games table found")
                    return {'games': [], 'count': 0}

                # Get all games from the table
                cursor.execute(f"SELECT * FROM {games_table} ORDER BY rowid DESC LIMIT 10")
                rows = cursor.fetchall()

                games = []
                for i, row in enumerate(rows):
                    games.append({
                        'game_id': str(row[0]) if len(row) > 0 else f'game_{i}',
                        'home_team': str(row[1]) if len(row) > 1 else 'HOME',
                        'away_team': str(row[2]) if len(row) > 2 else 'AWAY',
                        'home_score': int(row[3]) if len(row) > 3 and row[3] else 0,
                        'away_score': int(row[4]) if len(row) > 4 and row[4] else 0,
                        'quarter': int(row[5]) if len(row) > 5 and row[5] else 1,
                        'time_remaining': str(row[6]) if len(row) > 6 else '12:00',
                        'status': str(row[7]) if len(row) > 7 else 'live',
                        'start_time': str(row[8]) if len(row) > 8 else datetime.now().isoformat()
                    })

                return {'games': games, 'count': len(games)}

        except Exception as e:
            logger.error(f"❌ Games fetch error: {e}")
            return {'games': [], 'count': 0}
    
    def _fetch_real_odds_from_db(self) -> Dict[str, Any]:
        """Fetch real odds from expert odds database"""
        try:
            with sqlite3.connect(self.odds_db) as conn:
                cursor = conn.cursor()

                # Check what tables exist
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]

                # Try different possible table names
                odds_tables = ['fresh_odds', 'odds', 'game_odds', 'api_calls']
                odds_table = None

                for table in odds_tables:
                    if table in tables:
                        odds_table = table
                        break

                if not odds_table:
                    logger.warning("⚠️ No odds table found")
                    return {'odds': [], 'count': 0}

                # Get table schema
                cursor.execute(f"PRAGMA table_info({odds_table})")
                columns = [row[1] for row in cursor.fetchall()]

                # Get recent odds
                cursor.execute(f"SELECT * FROM {odds_table} ORDER BY rowid DESC LIMIT 20")
                rows = cursor.fetchall()

                odds = []
                for i, row in enumerate(rows):
                    odds.append({
                        'id': i,
                        'home_team': str(row[0]) if len(row) > 0 else 'HOME',
                        'away_team': str(row[1]) if len(row) > 1 else 'AWAY',
                        'odds_data': str(row[2]) if len(row) > 2 else '{}',
                        'market_type': 'h2h',
                        'last_updated': datetime.now().isoformat()
                    })

                return {'odds': odds, 'count': len(odds)}

        except Exception as e:
            logger.error(f"❌ Odds fetch error: {e}")
            return {'odds': [], 'count': 0}
    
    def _fetch_real_player_stats_from_db(self) -> Dict[str, Any]:
        """Fetch real live player stats from military-grade database"""
        try:
            with sqlite3.connect(self.military_db) as conn:
                cursor = conn.cursor()

                # Check what tables exist
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]

                # Try different possible table names
                stats_tables = ['live_player_stats', 'player_stats', 'boxscore', 'player_data']
                stats_table = None

                for table in stats_tables:
                    if table in tables:
                        stats_table = table
                        break

                if not stats_table:
                    logger.warning("⚠️ No player stats table found")
                    return {'player_stats': [], 'count': 0}

                # Get recent player stats
                cursor.execute(f"SELECT * FROM {stats_table} ORDER BY rowid DESC LIMIT 50")
                rows = cursor.fetchall()

                stats = []
                for i, row in enumerate(rows):
                    stats.append({
                        'player_name': str(row[0]) if len(row) > 0 else f'Player_{i}',
                        'team': str(row[1]) if len(row) > 1 else 'TEAM',
                        'game_id': str(row[2]) if len(row) > 2 else 'game_1',
                        'minutes_played': float(row[3]) if len(row) > 3 and row[3] else 0.0,
                        'points': int(row[4]) if len(row) > 4 and row[4] else 0,
                        'rebounds': int(row[5]) if len(row) > 5 and row[5] else 0,
                        'assists': int(row[6]) if len(row) > 6 and row[6] else 0,
                        'steals': int(row[7]) if len(row) > 7 and row[7] else 0,
                        'blocks': int(row[8]) if len(row) > 8 and row[8] else 0,
                        'turnovers': int(row[9]) if len(row) > 9 and row[9] else 0,
                        'fouls': int(row[10]) if len(row) > 10 and row[10] else 0,
                        'is_on_court': True,
                        'last_updated': datetime.now().isoformat()
                    })

                return {'player_stats': stats, 'count': len(stats)}

        except Exception as e:
            logger.error(f"❌ Player stats fetch error: {e}")
            return {'player_stats': [], 'count': 0}
    
    def _get_real_live_props(self):
        """Get cached real live props"""
        return self.live_props_cache
    
    def _get_real_live_games(self):
        """Get cached real live games"""
        return self.live_games_cache
    
    def _get_real_live_odds(self):
        """Get cached real live odds"""
        return self.live_odds_cache
    
    def _get_real_live_player_stats(self):
        """Get cached real live player stats"""
        return self.live_player_stats_cache
    
    def _get_real_play_by_play(self):
        """Get real play-by-play events"""
        try:
            with sqlite3.connect(self.military_db) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT event_id, game_id, quarter, time_remaining, event_type,
                           player_name, team, description, home_score, away_score,
                           shot_type, shot_made, assist_player, timestamp
                    FROM play_by_play 
                    WHERE timestamp > datetime('now', '-2 hours')
                    ORDER BY timestamp DESC
                    LIMIT 100
                """)
                
                events = []
                for row in cursor.fetchall():
                    events.append({
                        'event_id': row[0],
                        'game_id': row[1],
                        'quarter': row[2],
                        'time_remaining': row[3],
                        'event_type': row[4],
                        'player_name': row[5],
                        'team': row[6],
                        'description': row[7],
                        'home_score': row[8],
                        'away_score': row[9],
                        'shot_type': row[10],
                        'shot_made': row[11],
                        'assist_player': row[12],
                        'timestamp': row[13]
                    })
                
                return {'events': events, 'count': len(events)}
                
        except Exception as e:
            logger.error(f"❌ Play-by-play fetch error: {e}")
            return {'events': [], 'count': 0}
    
    def run(self):
        """Start the live data bridge server"""
        logger.info(f"🔗 Starting Live Data Bridge on port {self.port}")
        logger.info(f"🌐 Bridge API: http://127.0.0.1:{self.port}")
        logger.info("📊 Real-time data endpoints:")
        logger.info(f"   • Props: http://127.0.0.1:{self.port}/api/live/props")
        logger.info(f"   • Games: http://127.0.0.1:{self.port}/api/live/games")
        logger.info(f"   • Odds: http://127.0.0.1:{self.port}/api/live/odds")
        logger.info(f"   • Player Stats: http://127.0.0.1:{self.port}/api/live/player_stats")
        
        self.app.run(host='127.0.0.1', port=self.port, debug=False)

if __name__ == "__main__":
    bridge = LiveDataBridge()
    bridge.run()
