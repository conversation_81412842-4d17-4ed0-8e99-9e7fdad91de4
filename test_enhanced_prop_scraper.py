#!/usr/bin/env python3
"""
🧪 TEST ENHANCED PROP SCRAPER
============================

Quick test of the enhanced player props scraper with expert methods.
"""

import asyncio
import logging
from real_player_props_scraper import RealPlayerPropsScraper

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_enhanced_scraper():
    """Test the enhanced prop scraper"""
    print("🧪 Testing Enhanced WNBA Player Props Scraper")
    print("=" * 50)
    
    # Initialize scraper
    scraper = RealPlayerPropsScraper()
    
    # Test configuration
    print(f"✅ Sportsbooks configured: {len(scraper.sportsbooks)}")
    print(f"✅ WNBA players loaded: {len(scraper.wnba_players)}")
    print(f"✅ Prop types available: {len(scraper.prop_types)}")
    print(f"✅ User agents available: {len(scraper.user_agents)}")
    
    # Show specific URLs being used
    print("\n🎯 DraftKings Prop URLs:")
    dk_config = scraper.sportsbooks['draftkings']
    for prop_type, url in dk_config['prop_urls'].items():
        print(f"   • {prop_type}: {url}")
    
    print("\n🎯 bet365 Prop URLs:")
    bet365_config = scraper.sportsbooks['bet365']
    for prop_type, url in bet365_config['prop_urls'].items():
        print(f"   • {prop_type}: {url}")
    
    # Test driver creation
    print("\n🚗 Testing driver creation...")
    driver = scraper._create_driver()
    if driver:
        print("✅ Expert stealth driver created successfully")
        driver.quit()
    else:
        print("❌ Driver creation failed")
        return
    
    # Test scraping (just DraftKings for now)
    print("\n🕷️ Testing DraftKings scraping...")
    try:
        dk_props = await scraper.scrape_draftkings_props()
        print(f"✅ DraftKings scraping completed: {len(dk_props)} props found")
        
        if dk_props:
            print("\n📊 Sample props found:")
            for i, prop in enumerate(dk_props[:5]):
                print(f"   {i+1}. {prop.player_name} {prop.prop_type} {prop.line} ({prop.sportsbook})")
        else:
            print("ℹ️ No props found - this is normal if no games are scheduled")
            
    except Exception as e:
        print(f"❌ DraftKings test failed: {e}")
    
    print("\n🎯 Enhanced scraper test completed!")

if __name__ == "__main__":
    asyncio.run(test_enhanced_scraper())
