#!/usr/bin/env python3
"""
🌐 MANUAL PROPS COLLECTOR
=========================
Manual collection of WNBA player props from specific sportsbook pages
Opens browser pages for manual data collection and entry
"""

import sqlite3
import logging
from datetime import datetime, timedelta
import json
import webbrowser
import time
import schedule
import threading
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ManualPropsCollector:
    """Manual props collection system with browser page opening"""

    def __init__(self):
        self.db_path = 'military_grade_wnba_data.db'
        # Comprehensive DraftKings WNBA prop URLs
        self.draftkings_prop_urls = {
            'DK_Points': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-points&subcategory=points',
            'DK_Points_O/U': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-points&subcategory=points-o%2Fu',
            'DK_Threes': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-threes&subcategory=threes',
            'DK_Threes_O/U': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-threes&subcategory=threes-o%2Fu',
            'DK_Rebounds': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-rebounds&subcategory=rebounds',
            'DK_Rebounds_O/U': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-rebounds&subcategory=rebounds-o%2Fu',
            'DK_Assists': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-assists&subcategory=assists',
            'DK_Assists_O/U': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-assists&subcategory=assists-o%2Fu',
            'DK_PRA_Combo': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-combos&subcategory=pts-%2B-reb-%2B-ast',
            'DK_PR_Combo': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-combos&subcategory=pts-%2B-reb',
            'DK_PA_Combo': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-combos&subcategory=pts-%2B-ast',
            'DK_Scoring_Leader': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=game-leaders&subcategory=scoring-leader',
            'DK_H2H_Points': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=h2h-player-props&subcategory=h2h-points',
            'DK_H2H_Rebounds': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=h2h-player-props&subcategory=h2h-rebounds',
            'DK_H2H_Assists': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=h2h-player-props&subcategory=h2h-assists',
            'DK_Team_Totals': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=team-props&subcategory=team-totals'
        }

        # FanDuel specific URLs
        self.fanduel_prop_urls = {
            'FD_Parlay_Builder': 'https://sportsbook.fanduel.com/navigation/wnba?tab=parlay-builder',
            'FD_WNBA_Main': 'https://www.fanduel.com/sportsbook/basketball/wnba'
        }

        # Other sportsbooks (general URLs)
        self.sportsbook_urls = {
            'DraftKings_General': 'https://sportsbook.draftkings.com/leagues/basketball/wnba',
            'BetMGM': 'https://sports.betmgm.com/en/sports/basketball-7/betting/usa-9/wnba-2394',
            'Caesars': 'https://www.caesars.com/sportsbook/basketball/wnba',
            'bet365': 'https://www.bet365.com/#/AS/B18/C20604387/D43/E174/F43/',
            'ESPN BET': 'https://espnbet.com/sport/basketball/organization/wnba',
            'PointsBet': 'https://pointsbet.com/sports/basketball/wnba'
        }

        # Combine all URLs
        self.all_urls = {**self.draftkings_prop_urls, **self.fanduel_prop_urls, **self.sportsbook_urls}

    def open_sportsbook_pages(self, sportsbooks=None):
        """🌐 Open specific sportsbook pages for manual props collection"""
        if sportsbooks is None:
            sportsbooks = list(self.all_urls.keys())

        print(f"🌐 Opening {len(sportsbooks)} sportsbook pages...")

        for sportsbook in sportsbooks:
            if sportsbook in self.all_urls:
                url = self.all_urls[sportsbook]
                print(f"📖 Opening {sportsbook}: {url}")
                try:
                    webbrowser.open(url)
                    time.sleep(1.5)  # Wait between opens to avoid overwhelming browser
                    logger.info(f"✅ Opened {sportsbook} page")
                except Exception as e:
                    logger.error(f"❌ Failed to open {sportsbook}: {e}")
            else:
                logger.warning(f"⚠️ Unknown sportsbook: {sportsbook}")

        print(f"✅ Opened {len(sportsbooks)} sportsbook pages")
        print("💡 You can now manually collect props from the open pages")

    def open_all_draftkings_props(self):
        """🎯 Open ALL DraftKings prop pages (16 specific prop categories)"""
        print("🎯 Opening ALL 16 DraftKings WNBA prop pages...")

        for name, url in self.draftkings_prop_urls.items():
            print(f"📖 Opening {name}...")
            try:
                webbrowser.open(url)
                time.sleep(1.5)  # Wait between opens
                logger.info(f"✅ Opened {name}")
            except Exception as e:
                logger.error(f"❌ Failed to open {name}: {e}")

        print(f"✅ Opened all {len(self.draftkings_prop_urls)} DraftKings prop pages")
        print("🎯 All specific prop categories are now open for manual collection")

    def add_custom_url(self, sportsbook_name, url):
        """Add a custom URL for a sportsbook"""
        self.all_urls[sportsbook_name] = url
        logger.info(f"✅ Added custom URL for {sportsbook_name}: {url}")

    def list_available_sportsbooks(self):
        """List all available sportsbook URLs"""
        print("\n📋 DraftKings Specific Prop Pages:")
        print("-" * 60)
        for name, url in self.draftkings_prop_urls.items():
            print(f"{name}: {url}")

        print(f"\n📋 Other Sportsbooks:")
        print("-" * 60)
        for name, url in self.sportsbook_urls.items():
            print(f"{name}: {url}")

        print(f"\nTotal: {len(self.all_urls)} pages ({len(self.draftkings_prop_urls)} DK + {len(self.sportsbook_urls)} others)")
        
    def add_prop_manually(self, player_name, prop_type, line, over_odds, under_odds, sportsbook, team=None, game_id=None):
        """Add a single prop manually to the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Insert the prop
            cursor.execute('''
                INSERT INTO wnba_props 
                (player_name, prop_type, line, over_odds, under_odds, sportsbook, timestamp, team, game_id, confidence, expected_value, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                player_name,
                prop_type,
                float(line),
                int(over_odds),
                int(under_odds),
                sportsbook,
                datetime.now().isoformat(),
                team or 'WNBA',
                game_id or f"{player_name}_{prop_type}_{datetime.now().strftime('%Y%m%d')}",
                0.85,  # Default confidence
                0.05,  # Default expected value
                'active'
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Added prop: {player_name} {prop_type} {line} ({sportsbook})")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to add prop: {e}")
            return False
    
    def add_sample_props_for_today(self):
        """Add realistic sample props for today's games"""
        
        # Today's games and realistic props based on WNBA averages
        sample_props = [
            # Las Vegas Aces vs Seattle Storm
            ("A'ja Wilson", "points", 22.5, -110, -110, "DraftKings", "LAS", "LAS_vs_SEA_20250716"),
            ("A'ja Wilson", "rebounds", 9.5, -115, -105, "DraftKings", "LAS", "LAS_vs_SEA_20250716"),
            ("A'ja Wilson", "assists", 2.5, -120, +100, "DraftKings", "LAS", "LAS_vs_SEA_20250716"),
            ("Kelsey Plum", "points", 17.5, -110, -110, "DraftKings", "LAS", "LAS_vs_SEA_20250716"),
            ("Kelsey Plum", "assists", 4.5, -105, -115, "DraftKings", "LAS", "LAS_vs_SEA_20250716"),
            ("Jewell Loyd", "points", 19.5, -110, -110, "DraftKings", "SEA", "LAS_vs_SEA_20250716"),
            ("Nneka Ogwumike", "rebounds", 7.5, -110, -110, "DraftKings", "SEA", "LAS_vs_SEA_20250716"),
            
            # New York Liberty vs Connecticut Sun
            ("Breanna Stewart", "points", 20.5, -110, -110, "FanDuel", "NYL", "NYL_vs_CON_20250716"),
            ("Breanna Stewart", "rebounds", 8.5, -115, -105, "FanDuel", "NYL", "NYL_vs_CON_20250716"),
            ("Sabrina Ionescu", "points", 18.5, -110, -110, "FanDuel", "NYL", "NYL_vs_CON_20250716"),
            ("Sabrina Ionescu", "assists", 6.5, -105, -115, "FanDuel", "NYL", "NYL_vs_CON_20250716"),
            ("Sabrina Ionescu", "three_pointers", 2.5, -110, -110, "FanDuel", "NYL", "NYL_vs_CON_20250716"),
            ("Alyssa Thomas", "rebounds", 9.5, -110, -110, "FanDuel", "CON", "NYL_vs_CON_20250716"),
            ("Alyssa Thomas", "assists", 7.5, -115, -105, "FanDuel", "CON", "NYL_vs_CON_20250716"),
            
            # Chicago Sky vs Indiana Fever
            ("Angel Reese", "rebounds", 11.5, -110, -110, "BetMGM", "CHI", "CHI_vs_IND_20250716"),
            ("Angel Reese", "points", 14.5, -115, -105, "BetMGM", "CHI", "CHI_vs_IND_20250716"),
            ("Caitlin Clark", "points", 19.5, -110, -110, "BetMGM", "IND", "CHI_vs_IND_20250716"),
            ("Caitlin Clark", "assists", 8.5, -105, -115, "BetMGM", "IND", "CHI_vs_IND_20250716"),
            ("Caitlin Clark", "three_pointers", 3.5, -110, -110, "BetMGM", "IND", "CHI_vs_IND_20250716"),
            ("Kelsey Mitchell", "points", 16.5, -110, -110, "BetMGM", "IND", "CHI_vs_IND_20250716"),
            
            # Phoenix Mercury vs Minnesota Lynx
            ("Kahleah Copper", "points", 18.5, -110, -110, "Caesars", "PHO", "PHO_vs_MIN_20250716"),
            ("Diana Taurasi", "points", 15.5, -115, -105, "Caesars", "PHO", "PHO_vs_MIN_20250716"),
            ("Diana Taurasi", "three_pointers", 2.5, -110, -110, "Caesars", "PHO", "PHO_vs_MIN_20250716"),
            ("Napheesa Collier", "points", 21.5, -110, -110, "Caesars", "MIN", "PHO_vs_MIN_20250716"),
            ("Napheesa Collier", "rebounds", 8.5, -105, -115, "Caesars", "MIN", "PHO_vs_MIN_20250716"),
            ("Kayla McBride", "points", 14.5, -110, -110, "Caesars", "MIN", "PHO_vs_MIN_20250716"),
            
            # Atlanta Dream vs Washington Mystics
            ("Rhyne Howard", "points", 17.5, -110, -110, "bet365", "ATL", "ATL_vs_WAS_20250716"),
            ("Allisha Gray", "points", 15.5, -115, -105, "bet365", "ATL", "ATL_vs_WAS_20250716"),
            ("Ariel Atkins", "points", 16.5, -110, -110, "bet365", "WAS", "ATL_vs_WAS_20250716"),
            ("Brittney Sykes", "assists", 4.5, -105, -115, "bet365", "WAS", "ATL_vs_WAS_20250716"),
        ]
        
        success_count = 0
        for prop in sample_props:
            if self.add_prop_manually(*prop):
                success_count += 1
        
        logger.info(f"✅ Successfully added {success_count}/{len(sample_props)} props")
        return success_count
    
    def get_props_count(self):
        """Get current props count in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM wnba_props')
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except Exception as e:
            logger.error(f"❌ Failed to get props count: {e}")
            return 0
    
    def show_recent_props(self, limit=10):
        """Show recent props in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT player_name, prop_type, line, over_odds, under_odds, sportsbook, team
                FROM wnba_props 
                ORDER BY timestamp DESC 
                LIMIT ?
            ''', (limit,))
            
            props = cursor.fetchall()
            conn.close()
            
            if props:
                print(f"\n📊 Recent {len(props)} Props:")
                print("-" * 80)
                for prop in props:
                    player, prop_type, line, over_odds, under_odds, sportsbook, team = prop
                    print(f"{player} ({team}) - {prop_type} {line} | O{over_odds}/U{under_odds} | {sportsbook}")
            else:
                print("No props found in database")
                
        except Exception as e:
            logger.error(f"❌ Failed to show props: {e}")

    def remove_duplicate_props(self):
        """🔍 Remove duplicate props from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Find duplicates based on player_name, prop_type, and line
            cursor.execute('''
                SELECT player_name, prop_type, line, COUNT(*) as count
                FROM wnba_props
                GROUP BY player_name, prop_type, line
                HAVING COUNT(*) > 1
            ''')

            duplicates = cursor.fetchall()

            if not duplicates:
                print("✅ No duplicate props found")
                conn.close()
                return 0

            print(f"🔍 Found {len(duplicates)} sets of duplicate props:")

            removed_count = 0
            for player, prop_type, line, count in duplicates:
                print(f"  {player} {prop_type} {line} - {count} duplicates")

                # Keep the most recent one, remove older duplicates
                cursor.execute('''
                    DELETE FROM wnba_props
                    WHERE id NOT IN (
                        SELECT id FROM wnba_props
                        WHERE player_name = ? AND prop_type = ? AND line = ?
                        ORDER BY timestamp DESC
                        LIMIT 1
                    ) AND player_name = ? AND prop_type = ? AND line = ?
                ''', (player, prop_type, line, player, prop_type, line))

                removed_count += count - 1  # Keep 1, remove the rest

            conn.commit()
            conn.close()

            logger.info(f"🧹 Removed {removed_count} duplicate props")
            return removed_count

        except Exception as e:
            logger.error(f"❌ Failed to remove duplicates: {e}")
            return 0

    def get_unique_props_for_dashboard(self):
        """📊 Get unique props for Elite Prediction Chamber (no duplicates)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get unique props with preference for best odds and most recent
            cursor.execute('''
                SELECT
                    player_name,
                    prop_type,
                    line,
                    over_odds,
                    under_odds,
                    sportsbook,
                    team,
                    timestamp,
                    confidence,
                    expected_value,
                    MAX(timestamp) as latest_timestamp
                FROM wnba_props
                WHERE status = 'active'
                GROUP BY player_name, prop_type, line
                ORDER BY player_name, prop_type, line
            ''')

            props = cursor.fetchall()
            conn.close()

            # Convert to dashboard format
            dashboard_props = []
            for prop in props:
                dashboard_props.append({
                    'player_id': f"{prop[0].replace(' ', '_')}_{prop[1]}_{prop[2]}",
                    'player_name': prop[0],
                    'prop_type': prop[1],
                    'line': prop[2],
                    'over_odds': prop[3],
                    'under_odds': prop[4],
                    'sportsbook': prop[5],
                    'team': prop[6] or 'WNBA',
                    'timestamp': prop[7],
                    'confidence': (prop[8] or 0.5) * 100,  # Convert to percentage
                    'expected_value': prop[9] or 0.0,
                    'prediction': 'OVER' if (prop[9] or 0) > 0 else 'UNDER',
                    'recommendation': self._get_recommendation_level(prop[8] or 0.5, prop[9] or 0.0)
                })

            return dashboard_props

        except Exception as e:
            logger.error(f"❌ Failed to get unique props: {e}")
            return []

    def _get_recommendation_level(self, confidence, expected_value):
        """🎯 Get recommendation level based on confidence and EV"""
        if confidence >= 0.9 and expected_value >= 0.1:
            return "🔐 VAULT LOCK - ELITE PICK"
        elif confidence >= 0.8 and expected_value >= 0.05:
            return "🔥 HIGH CONFIDENCE"
        elif confidence >= 0.7:
            return "✅ SOLID PICK"
        elif confidence >= 0.6:
            return "⚠️ MODERATE RISK"
        else:
            return "❌ HIGH RISK"

    def clean_database(self):
        """🧹 Complete database cleanup - remove duplicates and old props"""
        try:
            print("🧹 Starting database cleanup...")

            # Remove duplicates
            removed_duplicates = self.remove_duplicate_props()

            # Remove old props (older than 24 hours)
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            yesterday = (datetime.now() - timedelta(days=1)).isoformat()
            cursor.execute('DELETE FROM wnba_props WHERE timestamp < ?', (yesterday,))
            removed_old = cursor.rowcount

            conn.commit()
            conn.close()

            print(f"✅ Database cleanup complete:")
            print(f"  🔍 Removed {removed_duplicates} duplicate props")
            print(f"  🗑️ Removed {removed_old} old props")

            return removed_duplicates + removed_old

        except Exception as e:
            logger.error(f"❌ Database cleanup failed: {e}")
            return 0

def interactive_menu():
    """Interactive menu for props collection"""
    collector = ManualPropsCollector()

    while True:
        print("\n🌐 MANUAL PROPS COLLECTOR")
        print("=" * 50)
        print("1. 🎯 Open ALL DraftKings prop pages (16 specific categories)")
        print("2. 📖 Open ALL sportsbook pages")
        print("3. 📖 Open specific sportsbook pages")
        print("4. 📋 List available sportsbooks")
        print("5. ➕ Add custom sportsbook URL")
        print("6. 📊 Show current props count")
        print("7. 📋 Show recent props")
        print("8. ➕ Add sample props for today")
        print("9. ➕ Add single prop manually")
        print("10. 🔍 Remove duplicate props")
        print("11. 🧹 Clean database (remove duplicates + old props)")
        print("12. 📊 Get unique props for dashboard")
        print("13. 🚪 Exit")

        choice = input("\n🎯 Choose an option (1-13): ").strip()

        if choice == '1':
            collector.open_all_draftkings_props()

        elif choice == '2':
            collector.open_sportsbook_pages()

        elif choice == '3':
            collector.list_available_sportsbooks()
            sportsbooks_input = input("\n📝 Enter sportsbook names (comma-separated): ").strip()
            if sportsbooks_input:
                sportsbooks = [s.strip() for s in sportsbooks_input.split(',')]
                collector.open_sportsbook_pages(sportsbooks)

        elif choice == '4':
            collector.list_available_sportsbooks()

        elif choice == '5':
            name = input("📝 Enter sportsbook name: ").strip()
            url = input("📝 Enter URL: ").strip()
            if name and url:
                collector.add_custom_url(name, url)

        elif choice == '6':
            count = collector.get_props_count()
            print(f"📊 Current props in database: {count}")

        elif choice == '7':
            limit = input("📝 How many recent props to show? (default 10): ").strip()
            limit = int(limit) if limit.isdigit() else 10
            collector.show_recent_props(limit)

        elif choice == '8':
            print("🎯 Adding sample props for today's games...")
            added_count = collector.add_sample_props_for_today()
            print(f"✅ Added {added_count} realistic props")

        elif choice == '9':
            print("➕ Add single prop manually:")
            player = input("Player name: ").strip()
            prop_type = input("Prop type (points/rebounds/assists/etc): ").strip()
            line = input("Line (e.g., 22.5): ").strip()
            over_odds = input("Over odds (e.g., -110): ").strip()
            under_odds = input("Under odds (e.g., -110): ").strip()
            sportsbook = input("Sportsbook: ").strip()
            team = input("Team (optional): ").strip()

            if all([player, prop_type, line, over_odds, under_odds, sportsbook]):
                try:
                    collector.add_prop_manually(
                        player, prop_type, float(line),
                        int(over_odds), int(under_odds), sportsbook,
                        team if team else None
                    )
                except ValueError:
                    print("❌ Invalid number format for line or odds")
            else:
                print("❌ Missing required fields")

        elif choice == '10':
            print("🔍 Removing duplicate props...")
            removed = collector.remove_duplicate_props()
            print(f"✅ Removed {removed} duplicate props")

        elif choice == '11':
            print("🧹 Cleaning database...")
            removed = collector.clean_database()
            print(f"✅ Database cleaned - removed {removed} total props")

        elif choice == '12':
            print("📊 Getting unique props for dashboard...")
            unique_props = collector.get_unique_props_for_dashboard()
            print(f"✅ Found {len(unique_props)} unique props for Elite Prediction Chamber")

            # Show sample of unique props
            if unique_props:
                print("\n📋 Sample unique props:")
                for prop in unique_props[:5]:
                    print(f"  {prop['player_name']} ({prop['team']}) - {prop['prop_type']} {prop['line']} | {prop['sportsbook']} | {prop['recommendation']}")

        elif choice == '13':
            print("👋 Goodbye!")
            break

        else:
            print("❌ Invalid choice. Please select 1-13.")

def main():
    """Main function to collect props"""
    print("🌐 MANUAL PROPS COLLECTOR")
    print("=" * 50)

    collector = ManualPropsCollector()

    # Check current props count
    current_count = collector.get_props_count()
    print(f"📊 Current props in database: {current_count}")

    # If no props, offer to add samples or go interactive
    if current_count == 0:
        choice = input("\n🎯 No props found. Add sample props? (y/n): ").strip().lower()
        if choice == 'y':
            added_count = collector.add_sample_props_for_today()
            print(f"✅ Added {added_count} realistic props for today's 5 WNBA games")

    # Show recent props
    collector.show_recent_props()

    # Ask if user wants interactive mode
    choice = input("\n🎯 Open interactive menu? (y/n): ").strip().lower()
    if choice == 'y':
        interactive_menu()
    else:
        print(f"\n🎉 Props collection complete!")
        print(f"📊 Total props now: {collector.get_props_count()}")

if __name__ == "__main__":
    main()
