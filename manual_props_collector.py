#!/usr/bin/env python3
"""
🌐 MANUAL PROPS COLLECTOR
=========================
Manual collection of WNBA player props from specific sportsbook pages
Opens browser pages for manual data collection and entry
"""

import sqlite3
import logging
from datetime import datetime
import json
import webbrowser
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ManualPropsCollector:
    """Manual props collection system with browser page opening"""

    def __init__(self):
        self.db_path = 'military_grade_wnba_data.db'
        self.sportsbook_urls = {
            'DraftKings': 'https://sportsbook.draftkings.com/leagues/basketball/wnba',
            'FanDuel': 'https://www.fanduel.com/sportsbook/basketball/wnba',
            'BetMGM': 'https://sports.betmgm.com/en/sports/basketball-7/betting/usa-9/wnba-2394',
            'Caesars': 'https://www.caesars.com/sportsbook/basketball/wnba',
            'bet365': 'https://www.bet365.com/#/AS/B18/C20604387/D43/E174/F43/',
            'ESPN BET': 'https://espnbet.com/sport/basketball/organization/wnba',
            'PointsBet': 'https://pointsbet.com/sports/basketball/wnba'
        }

    def open_sportsbook_pages(self, sportsbooks=None):
        """🌐 Open specific sportsbook pages for manual props collection"""
        if sportsbooks is None:
            sportsbooks = list(self.sportsbook_urls.keys())

        print(f"🌐 Opening {len(sportsbooks)} sportsbook pages...")

        for sportsbook in sportsbooks:
            if sportsbook in self.sportsbook_urls:
                url = self.sportsbook_urls[sportsbook]
                print(f"📖 Opening {sportsbook}: {url}")
                try:
                    webbrowser.open(url)
                    time.sleep(2)  # Wait between opens to avoid overwhelming browser
                    logger.info(f"✅ Opened {sportsbook} page")
                except Exception as e:
                    logger.error(f"❌ Failed to open {sportsbook}: {e}")
            else:
                logger.warning(f"⚠️ Unknown sportsbook: {sportsbook}")

        print(f"✅ Opened {len(sportsbooks)} sportsbook pages")
        print("💡 You can now manually collect props from the open pages")

    def add_custom_url(self, sportsbook_name, url):
        """Add a custom URL for a sportsbook"""
        self.sportsbook_urls[sportsbook_name] = url
        logger.info(f"✅ Added custom URL for {sportsbook_name}: {url}")

    def list_available_sportsbooks(self):
        """List all available sportsbook URLs"""
        print("\n📋 Available Sportsbooks:")
        print("-" * 50)
        for name, url in self.sportsbook_urls.items():
            print(f"{name}: {url}")
        print(f"\nTotal: {len(self.sportsbook_urls)} sportsbooks")
        
    def add_prop_manually(self, player_name, prop_type, line, over_odds, under_odds, sportsbook, team=None, game_id=None):
        """Add a single prop manually to the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Insert the prop
            cursor.execute('''
                INSERT INTO wnba_props 
                (player_name, prop_type, line, over_odds, under_odds, sportsbook, timestamp, team, game_id, confidence, expected_value, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                player_name,
                prop_type,
                float(line),
                int(over_odds),
                int(under_odds),
                sportsbook,
                datetime.now().isoformat(),
                team or 'WNBA',
                game_id or f"{player_name}_{prop_type}_{datetime.now().strftime('%Y%m%d')}",
                0.85,  # Default confidence
                0.05,  # Default expected value
                'active'
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Added prop: {player_name} {prop_type} {line} ({sportsbook})")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to add prop: {e}")
            return False
    
    def add_sample_props_for_today(self):
        """Add realistic sample props for today's games"""
        
        # Today's games and realistic props based on WNBA averages
        sample_props = [
            # Las Vegas Aces vs Seattle Storm
            ("A'ja Wilson", "points", 22.5, -110, -110, "DraftKings", "LAS", "LAS_vs_SEA_20250716"),
            ("A'ja Wilson", "rebounds", 9.5, -115, -105, "DraftKings", "LAS", "LAS_vs_SEA_20250716"),
            ("A'ja Wilson", "assists", 2.5, -120, +100, "DraftKings", "LAS", "LAS_vs_SEA_20250716"),
            ("Kelsey Plum", "points", 17.5, -110, -110, "DraftKings", "LAS", "LAS_vs_SEA_20250716"),
            ("Kelsey Plum", "assists", 4.5, -105, -115, "DraftKings", "LAS", "LAS_vs_SEA_20250716"),
            ("Jewell Loyd", "points", 19.5, -110, -110, "DraftKings", "SEA", "LAS_vs_SEA_20250716"),
            ("Nneka Ogwumike", "rebounds", 7.5, -110, -110, "DraftKings", "SEA", "LAS_vs_SEA_20250716"),
            
            # New York Liberty vs Connecticut Sun
            ("Breanna Stewart", "points", 20.5, -110, -110, "FanDuel", "NYL", "NYL_vs_CON_20250716"),
            ("Breanna Stewart", "rebounds", 8.5, -115, -105, "FanDuel", "NYL", "NYL_vs_CON_20250716"),
            ("Sabrina Ionescu", "points", 18.5, -110, -110, "FanDuel", "NYL", "NYL_vs_CON_20250716"),
            ("Sabrina Ionescu", "assists", 6.5, -105, -115, "FanDuel", "NYL", "NYL_vs_CON_20250716"),
            ("Sabrina Ionescu", "three_pointers", 2.5, -110, -110, "FanDuel", "NYL", "NYL_vs_CON_20250716"),
            ("Alyssa Thomas", "rebounds", 9.5, -110, -110, "FanDuel", "CON", "NYL_vs_CON_20250716"),
            ("Alyssa Thomas", "assists", 7.5, -115, -105, "FanDuel", "CON", "NYL_vs_CON_20250716"),
            
            # Chicago Sky vs Indiana Fever
            ("Angel Reese", "rebounds", 11.5, -110, -110, "BetMGM", "CHI", "CHI_vs_IND_20250716"),
            ("Angel Reese", "points", 14.5, -115, -105, "BetMGM", "CHI", "CHI_vs_IND_20250716"),
            ("Caitlin Clark", "points", 19.5, -110, -110, "BetMGM", "IND", "CHI_vs_IND_20250716"),
            ("Caitlin Clark", "assists", 8.5, -105, -115, "BetMGM", "IND", "CHI_vs_IND_20250716"),
            ("Caitlin Clark", "three_pointers", 3.5, -110, -110, "BetMGM", "IND", "CHI_vs_IND_20250716"),
            ("Kelsey Mitchell", "points", 16.5, -110, -110, "BetMGM", "IND", "CHI_vs_IND_20250716"),
            
            # Phoenix Mercury vs Minnesota Lynx
            ("Kahleah Copper", "points", 18.5, -110, -110, "Caesars", "PHO", "PHO_vs_MIN_20250716"),
            ("Diana Taurasi", "points", 15.5, -115, -105, "Caesars", "PHO", "PHO_vs_MIN_20250716"),
            ("Diana Taurasi", "three_pointers", 2.5, -110, -110, "Caesars", "PHO", "PHO_vs_MIN_20250716"),
            ("Napheesa Collier", "points", 21.5, -110, -110, "Caesars", "MIN", "PHO_vs_MIN_20250716"),
            ("Napheesa Collier", "rebounds", 8.5, -105, -115, "Caesars", "MIN", "PHO_vs_MIN_20250716"),
            ("Kayla McBride", "points", 14.5, -110, -110, "Caesars", "MIN", "PHO_vs_MIN_20250716"),
            
            # Atlanta Dream vs Washington Mystics
            ("Rhyne Howard", "points", 17.5, -110, -110, "bet365", "ATL", "ATL_vs_WAS_20250716"),
            ("Allisha Gray", "points", 15.5, -115, -105, "bet365", "ATL", "ATL_vs_WAS_20250716"),
            ("Ariel Atkins", "points", 16.5, -110, -110, "bet365", "WAS", "ATL_vs_WAS_20250716"),
            ("Brittney Sykes", "assists", 4.5, -105, -115, "bet365", "WAS", "ATL_vs_WAS_20250716"),
        ]
        
        success_count = 0
        for prop in sample_props:
            if self.add_prop_manually(*prop):
                success_count += 1
        
        logger.info(f"✅ Successfully added {success_count}/{len(sample_props)} props")
        return success_count
    
    def get_props_count(self):
        """Get current props count in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM wnba_props')
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except Exception as e:
            logger.error(f"❌ Failed to get props count: {e}")
            return 0
    
    def show_recent_props(self, limit=10):
        """Show recent props in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT player_name, prop_type, line, over_odds, under_odds, sportsbook, team
                FROM wnba_props 
                ORDER BY timestamp DESC 
                LIMIT ?
            ''', (limit,))
            
            props = cursor.fetchall()
            conn.close()
            
            if props:
                print(f"\n📊 Recent {len(props)} Props:")
                print("-" * 80)
                for prop in props:
                    player, prop_type, line, over_odds, under_odds, sportsbook, team = prop
                    print(f"{player} ({team}) - {prop_type} {line} | O{over_odds}/U{under_odds} | {sportsbook}")
            else:
                print("No props found in database")
                
        except Exception as e:
            logger.error(f"❌ Failed to show props: {e}")

def interactive_menu():
    """Interactive menu for props collection"""
    collector = ManualPropsCollector()

    while True:
        print("\n🌐 MANUAL PROPS COLLECTOR")
        print("=" * 50)
        print("1. 📖 Open ALL sportsbook pages")
        print("2. 📖 Open specific sportsbook pages")
        print("3. 📋 List available sportsbooks")
        print("4. ➕ Add custom sportsbook URL")
        print("5. 📊 Show current props count")
        print("6. 📋 Show recent props")
        print("7. ➕ Add sample props for today")
        print("8. ➕ Add single prop manually")
        print("9. 🚪 Exit")

        choice = input("\n🎯 Choose an option (1-9): ").strip()

        if choice == '1':
            collector.open_sportsbook_pages()

        elif choice == '2':
            collector.list_available_sportsbooks()
            sportsbooks_input = input("\n📝 Enter sportsbook names (comma-separated): ").strip()
            if sportsbooks_input:
                sportsbooks = [s.strip() for s in sportsbooks_input.split(',')]
                collector.open_sportsbook_pages(sportsbooks)

        elif choice == '3':
            collector.list_available_sportsbooks()

        elif choice == '4':
            name = input("📝 Enter sportsbook name: ").strip()
            url = input("📝 Enter URL: ").strip()
            if name and url:
                collector.add_custom_url(name, url)

        elif choice == '5':
            count = collector.get_props_count()
            print(f"📊 Current props in database: {count}")

        elif choice == '6':
            limit = input("📝 How many recent props to show? (default 10): ").strip()
            limit = int(limit) if limit.isdigit() else 10
            collector.show_recent_props(limit)

        elif choice == '7':
            print("🎯 Adding sample props for today's games...")
            added_count = collector.add_sample_props_for_today()
            print(f"✅ Added {added_count} realistic props")

        elif choice == '8':
            print("➕ Add single prop manually:")
            player = input("Player name: ").strip()
            prop_type = input("Prop type (points/rebounds/assists/etc): ").strip()
            line = input("Line (e.g., 22.5): ").strip()
            over_odds = input("Over odds (e.g., -110): ").strip()
            under_odds = input("Under odds (e.g., -110): ").strip()
            sportsbook = input("Sportsbook: ").strip()
            team = input("Team (optional): ").strip()

            if all([player, prop_type, line, over_odds, under_odds, sportsbook]):
                try:
                    collector.add_prop_manually(
                        player, prop_type, float(line),
                        int(over_odds), int(under_odds), sportsbook,
                        team if team else None
                    )
                except ValueError:
                    print("❌ Invalid number format for line or odds")
            else:
                print("❌ Missing required fields")

        elif choice == '9':
            print("👋 Goodbye!")
            break

        else:
            print("❌ Invalid choice. Please select 1-9.")

def main():
    """Main function to collect props"""
    print("🌐 MANUAL PROPS COLLECTOR")
    print("=" * 50)

    collector = ManualPropsCollector()

    # Check current props count
    current_count = collector.get_props_count()
    print(f"📊 Current props in database: {current_count}")

    # If no props, offer to add samples or go interactive
    if current_count == 0:
        choice = input("\n🎯 No props found. Add sample props? (y/n): ").strip().lower()
        if choice == 'y':
            added_count = collector.add_sample_props_for_today()
            print(f"✅ Added {added_count} realistic props for today's 5 WNBA games")

    # Show recent props
    collector.show_recent_props()

    # Ask if user wants interactive mode
    choice = input("\n🎯 Open interactive menu? (y/n): ").strip().lower()
    if choice == 'y':
        interactive_menu()
    else:
        print(f"\n🎉 Props collection complete!")
        print(f"📊 Total props now: {collector.get_props_count()}")

if __name__ == "__main__":
    main()
