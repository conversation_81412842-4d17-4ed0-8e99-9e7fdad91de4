#!/usr/bin/env python3
"""
🕷️ TEST ACTUAL SCRAPING
======================

Test the actual scraping functionality with enhanced anti-detection.
"""

import asyncio
import logging
from datetime import datetime
from real_player_props_scraper import RealPlayerPropsScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'actual_scraping_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

async def test_actual_scraping():
    """Test actual scraping with monitoring"""
    print("🕷️ Testing Actual WNBA Props Scraping")
    print("=" * 45)
    
    scraper = RealPlayerPropsScraper()
    
    # Test individual sportsbooks
    sportsbooks_to_test = [
        ('draftkings', scraper.scrape_draftkings_props),
        ('fanduel', scraper.scrape_fanduel_props),
    ]
    
    total_props = []
    
    for sportsbook_name, scrape_method in sportsbooks_to_test:
        print(f"\n🎯 Testing {sportsbook_name.upper()}...")
        print("-" * 30)
        
        try:
            start_time = datetime.now()
            props = await scrape_method()
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            print(f"   ⏱️ Duration: {duration:.1f} seconds")
            print(f"   📊 Props found: {len(props)}")
            
            if props:
                print(f"   ✅ SUCCESS! Sample props:")
                for i, prop in enumerate(props[:5]):
                    print(f"      {i+1}. {prop.player_name} {prop.prop_type} {prop.line} ({prop.over_odds}/{prop.under_odds})")
                
                total_props.extend(props)
                
                # Show prop breakdown
                prop_types = {}
                for prop in props:
                    prop_types[prop.prop_type] = prop_types.get(prop.prop_type, 0) + 1
                
                print(f"   📈 Prop breakdown:")
                for prop_type, count in prop_types.items():
                    print(f"      • {prop_type}: {count}")
                    
            else:
                print(f"   ℹ️ No props found (may be normal if no games scheduled)")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)[:100]}...")
            logger.error(f"Error scraping {sportsbook_name}: {e}")
    
    # Test comprehensive scraping
    print(f"\n🌐 Testing Comprehensive Multi-Sportsbook Scraping...")
    print("-" * 50)
    
    try:
        start_time = datetime.now()
        all_props = await scraper.scrape_all_real_props(max_sportsbooks=3)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"   ⏱️ Total duration: {duration:.1f} seconds")
        print(f"   📊 Total props: {len(all_props)}")
        
        if all_props:
            # Group by sportsbook
            by_sportsbook = {}
            by_prop_type = {}
            
            for prop in all_props:
                # By sportsbook
                if prop.sportsbook not in by_sportsbook:
                    by_sportsbook[prop.sportsbook] = []
                by_sportsbook[prop.sportsbook].append(prop)
                
                # By prop type
                by_prop_type[prop.prop_type] = by_prop_type.get(prop.prop_type, 0) + 1
            
            print(f"\n   📊 Results by sportsbook:")
            for sportsbook, props in by_sportsbook.items():
                print(f"      • {sportsbook}: {len(props)} props")
            
            print(f"\n   📊 Results by prop type:")
            for prop_type, count in sorted(by_prop_type.items(), key=lambda x: x[1], reverse=True):
                print(f"      • {prop_type}: {count}")
            
            print(f"\n   🎯 Top players found:")
            player_counts = {}
            for prop in all_props:
                player_counts[prop.player_name] = player_counts.get(prop.player_name, 0) + 1
            
            for player, count in sorted(player_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"      • {player}: {count} props")
                
        else:
            print(f"   ℹ️ No props found in comprehensive test")
            
    except Exception as e:
        print(f"   ❌ Comprehensive test error: {str(e)[:100]}...")
        logger.error(f"Comprehensive scraping error: {e}")
    
    # Summary and recommendations
    print(f"\n" + "="*50)
    print(f"📋 SCRAPING TEST SUMMARY")
    print(f"="*50)
    
    if total_props:
        print(f"✅ SUCCESS: Found {len(total_props)} props total")
        print(f"🎯 Scraper is working with current setup")
        print(f"\n💡 To improve results:")
        print(f"   • Add premium proxies for better success rate")
        print(f"   • Test during different times of day")
        print(f"   • Monitor logs for specific blocking patterns")
    else:
        print(f"⚠️ LIMITED SUCCESS: No props found")
        print(f"🔧 Likely causes:")
        print(f"   • Sportsbook blocking (need better proxies)")
        print(f"   • No WNBA games scheduled today")
        print(f"   • Props not available in current format")
        print(f"\n💡 Recommended actions:")
        print(f"   • Check logs for blocking messages")
        print(f"   • Add premium proxy service")
        print(f"   • Try during WNBA game days")
        print(f"   • Test with different sportsbooks")
    
    print(f"\n📄 Check log file for detailed information")
    print(f"🔍 Run monitor_scraper_performance.py for real-time analysis")

if __name__ == "__main__":
    asyncio.run(test_actual_scraping())
