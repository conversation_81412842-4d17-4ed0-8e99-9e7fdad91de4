#!/usr/bin/env python3
"""
🎯 START PRODUCTION TRAINING
============================================================
Quick script to start production training for today's games
"""

import asyncio
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def start_production_training():
    """Start production training for today's games"""
    try:
        logger.info("🎯 STARTING PRODUCTION TRAINING FOR TODAY'S GAMES")
        logger.info(f"📅 Date: {datetime.now().strftime('%Y-%m-%d')}")
        
        # Import Supreme Autopilot System
        from supreme_autopilot_system import SupremeAutopilotSystem
        
        # Initialize autopilot
        logger.info("🧠 Initializing Supreme Autopilot System...")
        autopilot = SupremeAutopilotSystem()
        
        # Train all models with expert parameters
        logger.info("🎯 Starting comprehensive model training...")
        training_results = await autopilot.auto_train_all_models()
        
        # Display results
        logger.info("🎉 PRODUCTION TRAINING COMPLETED")
        logger.info(f"📊 Models trained: {len(training_results.get('models_trained', []))}")
        logger.info(f"✅ Production ready: {len(training_results.get('production_ready', []))}")
        logger.info(f"🚀 Deployed: {len(training_results.get('deployed', []))}")
        
        if training_results.get('summary'):
            summary = training_results['summary']
            logger.info(f"📈 Success rate: {summary.get('success_rate', 0):.1%}")
            logger.info(f"🚀 Deployment rate: {summary.get('deployment_rate', 0):.1%}")
        
        # Check if ready for today's games
        deployed_count = len(training_results.get('deployed', []))
        if deployed_count >= 10:
            logger.info("✅ SYSTEM READY FOR TODAY'S GAMES")
        else:
            logger.warning(f"⚠️ Only {deployed_count} models deployed - may need manual intervention")
        
        return training_results
        
    except Exception as e:
        logger.error(f"❌ PRODUCTION TRAINING FAILED: {e}")
        return {'error': str(e)}

def main():
    """Main execution"""
    try:
        # Run production training
        results = asyncio.run(start_production_training())
        
        if 'error' in results:
            logger.error("❌ Training failed - manual intervention required")
            return 1
        else:
            logger.info("✅ Production training completed successfully")
            return 0
            
    except Exception as e:
        logger.error(f"❌ Execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
