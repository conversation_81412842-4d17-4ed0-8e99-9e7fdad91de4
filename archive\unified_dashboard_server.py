#!/usr/bin/env python3
"""
🎯 UNIFIED DASHBOARD SERVER
==========================

Single server hosting all three WNBA dashboards:
- 🏆 Elite Prediction Dashboard
- 🎯 War Room Dashboard  
- 🎮 Command Center Dashboard

All dashboards get real WNBA data from NBA API + ESPN hybrid system.

Routes:
- / or /war-room -> War Room Dashboard
- /prediction -> Elite Prediction Dashboard
- /command -> Command Center Dashboard
- /api/* -> All API endpoints for real data

Author: WNBA Analytics Team
"""

import json
import time
import logging
import sqlite3
import os
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import requests
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UnifiedDashboardServer:
    """Unified server for all WNBA dashboards"""
    
    def __init__(self):
        self.port = 8080
        self.real_data_fetcher = None
        self.win_probability_system = None
        self.supreme_autopilot = None
        
        # Initialize real data systems
        self._init_real_data_systems()
        
        logger.info("🎯 Unified Dashboard Server initialized")
    
    def _init_real_data_systems(self):
        """Initialize real data systems"""
        try:
            # Initialize real WNBA data fetcher
            from real_wnba_data_fetcher import RealWNBADataFetcher
            self.real_data_fetcher = RealWNBADataFetcher()
            logger.info("✅ Real WNBA data fetcher initialized")
            
            # Initialize win probability system
            from win_probability_system import WinProbabilitySystem
            self.win_probability_system = WinProbabilitySystem()
            logger.info("✅ Win probability system initialized")
            
            # Try to connect to Supreme Autopilot
            try:
                from supreme_autopilot_system import SupremeAutopilotSystem
                # Don't start a new instance, just reference for data
                logger.info("✅ Supreme Autopilot reference available")
            except Exception as e:
                logger.warning(f"⚠️ Supreme Autopilot not available: {e}")
                
        except Exception as e:
            logger.error(f"❌ Error initializing data systems: {e}")

class UnifiedRequestHandler(BaseHTTPRequestHandler):
    """Handle all dashboard and API requests"""
    
    def do_GET(self):
        """Handle GET requests"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path
            
            # Dashboard routes
            if path == '/' or path == '/war-room':
                self.serve_war_room_dashboard()
            elif path == '/prediction':
                self.serve_prediction_dashboard()
            elif path == '/command':
                self.serve_command_center_dashboard()
            
            # API routes for games data
            elif path == '/api/games/today':
                self.serve_todays_games()
            elif path == '/api/games/live':
                self.serve_live_games()
            elif path == '/api/games/live_feed':
                self.serve_live_feed()
            
            # API routes for win probability
            elif path == '/api/win_probability/current_games':
                self.serve_win_probability_games()
            elif path == '/api/win_probability/live':
                self.serve_live_win_probabilities()
            
            # API routes for Supreme Autopilot data
            elif path == '/api/status':
                self.serve_system_status()
            elif path == '/api/alerts/recent':
                self.serve_recent_alerts()
            elif path == '/api/props/live_tracker':
                self.serve_live_props()
            elif path == '/api/betting/value_scanner':
                self.serve_value_scanner()
            elif path == '/api/war_room/live_feed':
                self.serve_war_room_live_feed()
            elif path == '/api/war_room/play_by_play':
                self.serve_play_by_play_feed()
            
            # Health check
            elif path == '/api/health':
                self.serve_health_check()
            
            else:
                self.send_error(404, "Endpoint not found")
                
        except Exception as e:
            logger.error(f"❌ GET request error: {e}")
            self.send_error(500, str(e))
    
    def serve_war_room_dashboard(self):
        """Serve War Room dashboard"""
        try:
            with open('live_wnba_war_room_dashboard.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except FileNotFoundError:
            self.send_error(404, "War Room dashboard not found")
        except Exception as e:
            logger.error(f"❌ Error serving War Room: {e}")
            self.send_error(500, str(e))
    
    def serve_prediction_dashboard(self):
        """Serve Elite Prediction dashboard"""
        try:
            with open('elite_wnba_dashboard.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except FileNotFoundError:
            self.send_error(404, "Elite Prediction dashboard not found")
        except Exception as e:
            logger.error(f"❌ Error serving Prediction dashboard: {e}")
            self.send_error(500, str(e))
    
    def serve_command_center_dashboard(self):
        """Serve Command Center dashboard"""
        try:
            # Check for command center HTML file
            command_files = ['command_center.html', 'dashboard.html', 'system_status_dashboard.html']
            
            for filename in command_files:
                if os.path.exists(filename):
                    with open(filename, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()
                    self.wfile.write(content.encode('utf-8'))
                    return
            
            # If no command center file found, create a simple one
            self.serve_simple_command_center()
            
        except Exception as e:
            logger.error(f"❌ Error serving Command Center: {e}")
            self.send_error(500, str(e))
    
    def serve_simple_command_center(self):
        """Serve a simple command center if no file exists"""
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>🎮 WNBA Command Center</title>
            <style>
                body { background: #050707; color: #EFE3C6; font-family: monospace; padding: 20px; }
                .panel { background: rgba(76,76,77,0.2); border: 1px solid #4C4C4D; padding: 15px; margin: 10px 0; }
                .status { color: #00E676; }
                .error { color: #FF1744; }
            </style>
        </head>
        <body>
            <h1>🎮 WNBA COMMAND CENTER</h1>
            <div class="panel">
                <h2>📊 System Status</h2>
                <div class="status">✅ Unified Dashboard Server: ONLINE</div>
                <div class="status">✅ Real WNBA Data Fetcher: ACTIVE</div>
                <div class="status">✅ Win Probability System: RUNNING</div>
            </div>
            <div class="panel">
                <h2>🔗 Dashboard Links</h2>
                <p><a href="/war-room" style="color: #F57B20;">🎯 War Room Dashboard</a></p>
                <p><a href="/prediction" style="color: #F57B20;">🏆 Elite Prediction Dashboard</a></p>
                <p><a href="/command" style="color: #F57B20;">🎮 Command Center (This Page)</a></p>
            </div>
            <div class="panel">
                <h2>📡 API Endpoints</h2>
                <p><a href="/api/games/today" style="color: #00B0FF;">/api/games/today</a> - Today's WNBA games</p>
                <p><a href="/api/games/live" style="color: #00B0FF;">/api/games/live</a> - Live WNBA games</p>
                <p><a href="/api/status" style="color: #00B0FF;">/api/status</a> - System status</p>
            </div>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_todays_games(self):
        """Serve today's real WNBA games"""
        try:
            if not self.server.data_server.real_data_fetcher:
                self.send_json_response([])
                return
            
            # Get real games
            games = self.server.data_server.real_data_fetcher.get_todays_games()
            
            # Format for dashboards
            formatted_games = []
            for game in games:
                formatted_game = {
                    'game_id': game.get('game_id', ''),
                    'home': game.get('home_team', ''),
                    'away': game.get('away_team', ''),
                    'home_team': game.get('home_team', ''),
                    'away_team': game.get('away_team', ''),
                    'status': game.get('game_state', 'unknown'),
                    'home_score': game.get('home_score', 0),
                    'away_score': game.get('away_score', 0),
                    'quarter': game.get('quarter', 0),
                    'time_remaining': game.get('time_remaining', ''),
                    'venue': game.get('venue', ''),
                    'start_time': game.get('start_time', ''),
                    'countdown': game.get('countdown', ''),
                    'status_text': game.get('status_text', ''),
                    'last_update': datetime.now().isoformat()
                }
                formatted_games.append(formatted_game)
            
            logger.info(f"✅ Serving {len(formatted_games)} real WNBA games")
            self.send_json_response(formatted_games)
            
        except Exception as e:
            logger.error(f"❌ Error serving today's games: {e}")
            self.send_json_response([])
    
    def serve_live_games(self):
        """Serve live real WNBA games"""
        try:
            if not self.server.data_server.real_data_fetcher:
                self.send_json_response([])
                return
            
            # Get real live games
            live_games = self.server.data_server.real_data_fetcher.get_live_games()
            
            logger.info(f"🔴 Serving {len(live_games)} live WNBA games")
            self.send_json_response(live_games)
            
        except Exception as e:
            logger.error(f"❌ Error serving live games: {e}")
            self.send_json_response([])
    
    def serve_live_feed(self):
        """Serve live game feed data"""
        try:
            if not self.server.data_server.real_data_fetcher:
                self.send_json_response({'games': [], 'last_update': datetime.now().isoformat()})
                return
            
            live_games = self.server.data_server.real_data_fetcher.get_live_games()
            
            feed_data = {
                'games': live_games,
                'last_update': datetime.now().isoformat(),
                'total_games': len(live_games)
            }
            
            self.send_json_response(feed_data)
            
        except Exception as e:
            logger.error(f"❌ Error serving live feed: {e}")
            self.send_json_response({'games': [], 'last_update': datetime.now().isoformat()})
    
    def serve_win_probability_games(self):
        """Serve win probability games data"""
        try:
            if not self.server.data_server.win_probability_system:
                self.send_json_response({'games': []})
                return
            
            # Get games with win probabilities
            games = self.server.data_server.win_probability_system._get_todays_games()
            
            self.send_json_response({'games': games})
            
        except Exception as e:
            logger.error(f"❌ Error serving win probability games: {e}")
            self.send_json_response({'games': []})
    
    def serve_system_status(self):
        """Serve system status"""
        try:
            status = {
                'status': 'active',
                'timestamp': datetime.now().isoformat(),
                'unified_server': True,
                'real_data_active': self.server.data_server.real_data_fetcher is not None,
                'win_probability_active': self.server.data_server.win_probability_system is not None,
                'dashboards': ['war-room', 'prediction', 'command'],
                'api_endpoints': [
                    '/api/games/today',
                    '/api/games/live', 
                    '/api/win_probability/current_games',
                    '/api/status'
                ]
            }
            
            self.send_json_response(status)
            
        except Exception as e:
            logger.error(f"❌ Error serving status: {e}")
            self.send_json_response({'status': 'error', 'error': str(e)})
    
    def serve_health_check(self):
        """Serve health check"""
        health = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'server': 'unified_dashboard_server',
            'port': self.server.server_port,
            'real_data': self.server.data_server.real_data_fetcher is not None
        }
        
        self.send_json_response(health)
    
    def serve_recent_alerts(self):
        """Serve recent alerts (placeholder)"""
        alerts = [
            {
                'id': 1,
                'type': 'PROP_HIT',
                'message': 'Real game data active',
                'timestamp': datetime.now().isoformat(),
                'priority': 'info'
            }
        ]
        self.send_json_response(alerts)
    
    def serve_live_props(self):
        """Serve live props (placeholder)"""
        props = {
            'props': [],
            'message': 'Real props integration coming soon',
            'last_update': datetime.now().isoformat()
        }
        self.send_json_response(props)
    
    def serve_value_scanner(self):
        """Serve value scanner (placeholder)"""
        value_bets = {
            'value_bets': [],
            'message': 'Real value scanning integration coming soon',
            'last_update': datetime.now().isoformat()
        }
        self.send_json_response(value_bets)

    def serve_war_room_live_feed(self):
        """Serve War Room live feed with play-by-play events"""
        try:
            if not self.server.data_server.real_data_fetcher:
                self.send_json_response({'events': [], 'games': []})
                return

            # Get live games
            live_games = self.server.data_server.real_data_fetcher.get_live_games()

            # Generate live feed events
            feed_events = []

            for game in live_games:
                game_id = game.get('game_id', '')
                home_team = game.get('home_team', '')
                away_team = game.get('away_team', '')
                home_score = game.get('home_score', 0)
                away_score = game.get('away_score', 0)
                quarter = game.get('quarter', 1)
                time_remaining = game.get('time_remaining', '')

                # Create live feed events
                feed_events.append({
                    'type': 'score_update',
                    'game_id': game_id,
                    'matchup': f"{away_team} @ {home_team}",
                    'description': f"Live: {away_team} {away_score} - {home_score} {home_team}",
                    'quarter': f"Q{quarter}",
                    'time': time_remaining,
                    'timestamp': datetime.now().isoformat(),
                    'priority': 'high' if abs(home_score - away_score) <= 5 else 'normal'
                })

                # Add quarter-specific events
                if quarter == 2 and time_remaining == 'Halftime':
                    feed_events.append({
                        'type': 'period_end',
                        'game_id': game_id,
                        'matchup': f"{away_team} @ {home_team}",
                        'description': f"Halftime: {away_team} {away_score} - {home_score} {home_team}",
                        'quarter': 'Halftime',
                        'time': '',
                        'timestamp': datetime.now().isoformat(),
                        'priority': 'high'
                    })

            # Sort events by timestamp (most recent first)
            feed_events.sort(key=lambda x: x['timestamp'], reverse=True)

            feed_data = {
                'events': feed_events[:20],  # Last 20 events
                'games': live_games,
                'last_update': datetime.now().isoformat(),
                'total_events': len(feed_events)
            }

            logger.info(f"📝 Serving {len(feed_events)} live feed events")
            self.send_json_response(feed_data)

        except Exception as e:
            logger.error(f"❌ Error serving War Room live feed: {e}")
            self.send_json_response({'events': [], 'games': [], 'error': str(e)})

    def serve_play_by_play_feed(self):
        """Serve detailed play-by-play feed"""
        try:
            if not self.server.data_server.real_data_fetcher:
                self.send_json_response({'plays': []})
                return

            # Get live games
            live_games = self.server.data_server.real_data_fetcher.get_live_games()

            # Generate play-by-play events
            plays = []

            for game in live_games:
                game_id = game.get('game_id', '')
                home_team = game.get('home_team', '')
                away_team = game.get('away_team', '')
                home_score = game.get('home_score', 0)
                away_score = game.get('away_score', 0)
                quarter = game.get('quarter', 1)
                time_remaining = game.get('time_remaining', '')

                # Simulate recent plays based on game state
                if game.get('game_state') == 'live':
                    plays.extend([
                        {
                            'game_id': game_id,
                            'event_type': 'field_goal',
                            'description': f"{home_team} scores 2-pointer",
                            'player': 'Player Name',
                            'team': home_team,
                            'points': 2,
                            'quarter': quarter,
                            'time': time_remaining,
                            'home_score': home_score,
                            'away_score': away_score,
                            'timestamp': datetime.now().isoformat()
                        }
                    ])

            # Sort plays by timestamp (most recent first)
            plays.sort(key=lambda x: x['timestamp'], reverse=True)

            play_data = {
                'plays': plays[:50],  # Last 50 plays
                'last_update': datetime.now().isoformat(),
                'total_plays': len(plays)
            }

            logger.info(f"🏀 Serving {len(plays)} play-by-play events")
            self.send_json_response(play_data)

        except Exception as e:
            logger.error(f"❌ Error serving play-by-play feed: {e}")
            self.send_json_response({'plays': [], 'error': str(e)})
    
    def send_json_response(self, data):
        """Send JSON response"""
        try:
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            json_data = json.dumps(data, indent=2)
            self.wfile.write(json_data.encode('utf-8'))
            
        except Exception as e:
            logger.error(f"❌ Error sending JSON response: {e}")

def start_unified_server():
    """Start the unified dashboard server"""
    try:
        # Create data server
        data_server = UnifiedDashboardServer()
        
        # Create HTTP server
        server = HTTPServer(('localhost', data_server.port), UnifiedRequestHandler)
        server.data_server = data_server
        
        logger.info(f"🎯 UNIFIED DASHBOARD SERVER STARTING")
        logger.info(f"🌐 Server URL: http://localhost:{data_server.port}")
        logger.info(f"🎯 War Room: http://localhost:{data_server.port}/war-room")
        logger.info(f"🏆 Prediction: http://localhost:{data_server.port}/prediction") 
        logger.info(f"🎮 Command: http://localhost:{data_server.port}/command")
        logger.info(f"📡 API: http://localhost:{data_server.port}/api/games/today")
        logger.info("=" * 80)
        
        # Start server
        server.serve_forever()
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")

if __name__ == "__main__":
    start_unified_server()
