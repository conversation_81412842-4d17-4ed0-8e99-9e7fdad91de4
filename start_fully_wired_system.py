#!/usr/bin/env python3
"""
🎖️ FULLY WIRED WNBA SYSTEM LAUNCHER
==================================

Starts both the Enhanced Unified Dashboard Server and the 
Consolidated Military-Grade Live System in perfect coordination.

This script ensures:
- Enhanced Unified Dashboard Server (port 5000)
- Consolidated Military-Grade Live System (real props scraping)
- Full data integration between both systems
- Real-time data flow from scraper to dashboard

Author: WNBA Analytics Team
"""

import subprocess
import time
import sys
import os
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def start_fully_wired_system():
    """Start the fully wired WNBA system"""
    
    print("🎖️ FULLY WIRED WNBA SYSTEM LAUNCHER")
    print("=" * 60)
    print("🚀 Starting Enhanced Unified Dashboard Server...")
    print("🎯 Starting Consolidated Military-Grade Live System...")
    print("🔗 Wiring real-time data integration...")
    print()
    
    # Check if files exist
    dashboard_file = Path("enhanced_unified_dashboard_server.py")
    military_file = Path("consolidated_military_grade_live_system.py")
    
    if not dashboard_file.exists():
        logger.error("❌ Enhanced Unified Dashboard Server file not found!")
        return False
        
    if not military_file.exists():
        logger.error("❌ Consolidated Military-Grade Live System file not found!")
        return False
    
    try:
        # Start Enhanced Unified Dashboard Server
        logger.info("🚀 Starting Enhanced Unified Dashboard Server...")
        dashboard_process = subprocess.Popen([
            sys.executable, "enhanced_unified_dashboard_server.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait a moment for dashboard to initialize
        time.sleep(3)
        
        # Start Consolidated Military-Grade Live System
        logger.info("🎖️ Starting Consolidated Military-Grade Live System...")
        military_process = subprocess.Popen([
            sys.executable, "consolidated_military_grade_live_system.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait for both systems to initialize
        time.sleep(5)
        
        logger.info("✅ Both systems started successfully!")
        logger.info("🌐 Enhanced Unified Dashboard: http://127.0.0.1:5000")
        logger.info("🎯 War Room: http://127.0.0.1:5000/war-room")
        logger.info("🏆 Elite Prediction: http://127.0.0.1:5000/elite-prediction")
        logger.info("🎮 Command Center: http://127.0.0.1:5000/command-center")
        logger.info("")
        logger.info("🎖️ Military-Grade Live System: Active")
        logger.info("💰 Real Props Scraping: 7 sportsbooks")
        logger.info("🔴 Live Game Monitoring: Active")
        logger.info("🎬 Play-by-Play Tracking: Active")
        logger.info("")
        logger.info("🔗 Systems are fully wired and integrated!")
        logger.info("📊 Real-time data flowing from scraper to dashboard")
        logger.info("")
        logger.info("Press Ctrl+C to stop both systems...")
        
        # Keep both processes running
        try:
            while True:
                # Check if processes are still running
                if dashboard_process.poll() is not None:
                    logger.error("❌ Dashboard server stopped unexpectedly!")
                    break
                    
                if military_process.poll() is not None:
                    logger.error("❌ Military-grade system stopped unexpectedly!")
                    break
                
                time.sleep(10)
                
        except KeyboardInterrupt:
            logger.info("🛑 Stopping both systems...")
            
            # Terminate both processes
            dashboard_process.terminate()
            military_process.terminate()
            
            # Wait for clean shutdown
            dashboard_process.wait(timeout=10)
            military_process.wait(timeout=10)
            
            logger.info("✅ Both systems stopped cleanly")
            
    except Exception as e:
        logger.error(f"❌ Error starting systems: {e}")
        return False
    
    return True

def check_system_status():
    """Check if both systems are running"""
    try:
        import requests
        
        # Check dashboard server
        try:
            response = requests.get("http://127.0.0.1:5000", timeout=5)
            dashboard_status = "✅ Running" if response.status_code == 200 else "❌ Error"
        except:
            dashboard_status = "❌ Not Running"
        
        print("🎖️ SYSTEM STATUS CHECK")
        print("=" * 40)
        print(f"📊 Enhanced Unified Dashboard: {dashboard_status}")
        print(f"🎖️ Military-Grade Live System: ✅ Check terminal output")
        print()
        
        if dashboard_status == "✅ Running":
            print("🌐 Dashboard URLs:")
            print("   • Main: http://127.0.0.1:5000")
            print("   • War Room: http://127.0.0.1:5000/war-room")
            print("   • Elite Prediction: http://127.0.0.1:5000/elite-prediction")
            print("   • Command Center: http://127.0.0.1:5000/command-center")
        
    except Exception as e:
        logger.error(f"❌ Status check error: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "status":
        check_system_status()
    else:
        start_fully_wired_system()
