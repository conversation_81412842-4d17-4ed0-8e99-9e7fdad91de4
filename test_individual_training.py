#!/usr/bin/env python3
"""
Test script for individual model training
"""

import asyncio
import sys
from consolidated_real_ml_training_system import ConsolidatedRealMLTrainingSystem

async def test_individual_training():
    print('Testing FIXED individual model training...')
    
    try:
        system = ConsolidatedRealMLTrainingSystem()
        print('System initialized successfully')
        
        # Test individual training directly
        results = await system._train_real_individual_models()
        print('SUCCESS! Individual training results:')
        print(f'  Models trained: {len(results["models_trained"])}')
        print(f'  Total epochs: {results["total_epochs"]}')
        print(f'  Training time: {results["training_time"]:.1f}s')
        
        if results["models_trained"]:
            print('  First few models:')
            for i, model in enumerate(results["models_trained"][:5]):
                print(f'    {i+1}. {model}')
        
    except Exception as e:
        print(f'ERROR: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_individual_training())
