#!/usr/bin/env python3
"""
Test script for individual model training
"""

import asyncio
import sys
from consolidated_real_ml_training_system import ConsolidatedRealMLTrainingSystem

async def test_individual_training():
    print('Testing FIXED individual model training with REAL WNBA data...')

    try:
        system = ConsolidatedRealMLTrainingSystem()
        print('System initialized successfully')

        # Test individual training directly
        print('Starting individual model training...')
        results = await system._train_real_individual_models()

        print('\n' + '='*60)
        print('SUCCESS! Individual training results:')
        print(f'  Models trained: {len(results["models_trained"])}')
        print(f'  Total epochs: {results["total_epochs"]:,}')
        print(f'  Training time: {results["training_time"]:.1f}s')

        if results["models_trained"]:
            print(f'\n  All {len(results["models_trained"])} models trained:')
            for i, model in enumerate(results["models_trained"], 1):
                print(f'    {i:2d}. {model}')

        if results["model_metrics"]:
            print(f'\n  Sample training metrics:')
            for model, metrics in list(results["model_metrics"].items())[:3]:
                print(f'    {model}: {metrics["epochs_trained"]} epochs, MAE: {metrics["final_val_mae"]:.3f}')

    except Exception as e:
        print(f'ERROR: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_individual_training())
