{"scheduler_settings": {"daily_collection_time": "01:05", "collection_duration_minutes": 60, "weekly_cleanup_time": "01:00", "weekly_cleanup_day": "sunday", "max_retries": 3, "process_check_interval_seconds": 60}, "data_collection": {"sportsbooks": ["DraftKings", "FanDuel", "BetMGM", "Caesars", "bet365", "ESPN BET", "PointsBet"], "data_sources": ["NBA_API", "ESPN_API", "Odds_API"], "prop_types": ["points", "rebounds", "assists", "steals", "blocks", "three_pointers", "field_goals"]}, "database_settings": {"cleanup_older_than_days": 7, "vacuum_frequency": "weekly", "backup_enabled": true, "backup_retention_days": 30}, "logging": {"level": "INFO", "file": "daily_military_scheduler.log", "max_file_size_mb": 10, "backup_count": 5}, "notifications": {"success_notifications": true, "error_notifications": true, "daily_summary": true}, "system_monitoring": {"memory_threshold_mb": 1000, "cpu_threshold_percent": 80, "disk_space_threshold_gb": 5}}