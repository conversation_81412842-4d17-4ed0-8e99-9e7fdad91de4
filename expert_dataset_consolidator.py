#!/usr/bin/env python3
"""
🎯 EXPERT WNBA DATASET CONSOLIDATOR
==================================

Consolidate all WNBA data sources into a single expert dataset for training 46 models.
Handles 129,000+ data points with expert feature engineering and validation.
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import sqlite3
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExpertDatasetConsolidator:
    """🎯 Expert WNBA dataset consolidation for 46-model system"""
    
    def __init__(self):
        self.data_sources = []
        self.consolidated_df = None
        self.feature_columns = []
        self.target_columns = []
        self.metadata = {}
        
        logger.info("🎯 Expert Dataset Consolidator initialized")
    
    def discover_data_sources(self) -> List[Dict[str, Any]]:
        """🔍 Discover all available WNBA data sources"""
        logger.info("🔍 Discovering WNBA data sources...")
        
        sources = []
        
        # Primary training data locations
        primary_locations = [
            "New folder/consolidated_wnba/ml_training/wnba_training_data.csv",
            "New folder/consolidated_wnba/04_training_data/player_props/real_wnba_stats_training_data.csv", 
            "New folder/consolidated_wnba/04_training_data/player_props/clean_wnba_training_data.csv",
            "wnba_definitive_master_dataset_FIXED.csv",
            "wnba_ultimate_expert_dataset_cleaned.csv"
        ]
        
        for location in primary_locations:
            path = Path(location)
            if path.exists() and path.stat().st_size > 1024:  # > 1KB
                try:
                    # Quick analysis
                    df_sample = pd.read_csv(path, nrows=100)
                    size_mb = path.stat().st_size / 1024 / 1024
                    
                    source_info = {
                        'path': str(path),
                        'size_mb': size_mb,
                        'columns': len(df_sample.columns),
                        'sample_rows': len(df_sample),
                        'key_columns': self._identify_key_columns(df_sample),
                        'data_quality': self._assess_data_quality(df_sample),
                        'priority': self._calculate_priority(df_sample, size_mb)
                    }
                    
                    sources.append(source_info)
                    logger.info(f"✅ Found: {path.name} ({size_mb:.1f} MB, {len(df_sample.columns)} cols)")
                    
                except Exception as e:
                    logger.warning(f"⚠️ Could not analyze {path}: {e}")
        
        # Sort by priority (highest first)
        sources.sort(key=lambda x: x['priority'], reverse=True)
        
        self.data_sources = sources
        logger.info(f"📊 Discovered {len(sources)} data sources")
        
        return sources
    
    def _identify_key_columns(self, df: pd.DataFrame) -> List[str]:
        """🔍 Identify key WNBA columns in dataset"""
        key_patterns = {
            'player': ['player_name', 'player', 'name'],
            'team': ['team', 'team_name', 'team_abbreviation'],
            'stats': ['points', 'rebounds', 'assists', 'steals', 'blocks'],
            'game': ['game_date', 'date', 'game_id', 'season'],
            'advanced': ['usage_rate', 'efficiency', 'plus_minus', 'minutes']
        }
        
        found_columns = []
        for category, patterns in key_patterns.items():
            for pattern in patterns:
                matching_cols = [col for col in df.columns if pattern.lower() in col.lower()]
                found_columns.extend(matching_cols)
        
        return list(set(found_columns))
    
    def _assess_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """📊 Assess data quality metrics"""
        return {
            'missing_percentage': (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100,
            'duplicate_percentage': (df.duplicated().sum() / len(df)) * 100,
            'numeric_columns': len(df.select_dtypes(include=[np.number]).columns),
            'categorical_columns': len(df.select_dtypes(include=['object']).columns)
        }
    
    def _calculate_priority(self, df: pd.DataFrame, size_mb: float) -> float:
        """🎯 Calculate data source priority score"""
        score = 0
        
        # Size factor (larger is better, up to a point)
        score += min(size_mb / 10, 5)  # Max 5 points for size
        
        # Column count factor
        score += min(len(df.columns) / 10, 3)  # Max 3 points for columns
        
        # Key column bonus
        key_columns = self._identify_key_columns(df)
        score += len(key_columns) * 0.5  # 0.5 points per key column
        
        # Data quality bonus
        quality = self._assess_data_quality(df)
        if quality['missing_percentage'] < 10:
            score += 2
        if quality['duplicate_percentage'] < 5:
            score += 1
        
        return score
    
    def consolidate_datasets(self) -> pd.DataFrame:
        """🔄 Consolidate all data sources into expert dataset"""
        logger.info("🔄 Starting dataset consolidation...")
        
        if not self.data_sources:
            self.discover_data_sources()
        
        if not self.data_sources:
            raise ValueError("No data sources found for consolidation")
        
        # Start with highest priority source
        primary_source = self.data_sources[0]
        logger.info(f"📊 Loading primary source: {primary_source['path']}")
        
        try:
            self.consolidated_df = pd.read_csv(primary_source['path'])
            logger.info(f"✅ Loaded {len(self.consolidated_df):,} rows from primary source")
            
            # Add additional sources if they complement the primary
            for source in self.data_sources[1:]:
                if source['size_mb'] > 1:  # Only merge substantial sources
                    try:
                        additional_df = pd.read_csv(source['path'])
                        
                        # Smart merge based on common columns
                        common_cols = set(self.consolidated_df.columns) & set(additional_df.columns)
                        if len(common_cols) >= 3:  # Need at least 3 common columns
                            logger.info(f"🔗 Merging {source['path']} ({len(additional_df):,} rows)")
                            self.consolidated_df = self._smart_merge(self.consolidated_df, additional_df)
                        
                    except Exception as e:
                        logger.warning(f"⚠️ Could not merge {source['path']}: {e}")
            
            # Expert feature engineering
            self.consolidated_df = self._expert_feature_engineering(self.consolidated_df)
            
            # Data validation and cleaning
            self.consolidated_df = self._expert_data_cleaning(self.consolidated_df)
            
            logger.info(f"🎯 Final consolidated dataset: {len(self.consolidated_df):,} rows, {len(self.consolidated_df.columns)} columns")
            
            return self.consolidated_df
            
        except Exception as e:
            logger.error(f"❌ Consolidation failed: {e}")
            raise
    
    def _smart_merge(self, df1: pd.DataFrame, df2: pd.DataFrame) -> pd.DataFrame:
        """🧠 Smart merge of two datasets"""
        # Find best merge strategy
        common_cols = list(set(df1.columns) & set(df2.columns))
        
        # Try different merge strategies
        if 'player_name' in common_cols and 'game_date' in common_cols:
            # Player-game level merge
            return pd.concat([df1, df2], ignore_index=True).drop_duplicates(
                subset=['player_name', 'game_date'], keep='first'
            )
        elif 'player_name' in common_cols:
            # Player level merge
            return pd.concat([df1, df2], ignore_index=True).drop_duplicates(
                subset=['player_name'], keep='first'
            )
        else:
            # Simple concatenation
            return pd.concat([df1, df2], ignore_index=True)
    
    def _expert_feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """🎯 Expert feature engineering for 46-model system"""
        logger.info("🎯 Applying expert feature engineering...")
        
        # Add advanced metrics if basic stats exist
        if all(col in df.columns for col in ['points', 'rebounds', 'assists']):
            df['total_stats'] = df['points'] + df['rebounds'] + df['assists']
            df['efficiency_score'] = df['points'] / (df.get('field_goal_attempts', 1) + 1)
        
        # Add temporal features if date exists
        date_cols = [col for col in df.columns if 'date' in col.lower()]
        if date_cols:
            date_col = date_cols[0]
            df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
            df['season'] = df[date_col].dt.year
            df['month'] = df[date_col].dt.month
            df['day_of_week'] = df[date_col].dt.dayofweek
        
        return df
    
    def _expert_data_cleaning(self, df: pd.DataFrame) -> pd.DataFrame:
        """🧹 Expert data cleaning and validation"""
        logger.info("🧹 Applying expert data cleaning...")
        
        initial_rows = len(df)
        
        # Remove completely empty rows
        df = df.dropna(how='all')
        
        # Handle missing values intelligently
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if df[col].isnull().sum() > 0:
                df[col] = df[col].fillna(df[col].median())
        
        # Remove obvious outliers (beyond reasonable WNBA stats)
        if 'points' in df.columns:
            df = df[df['points'] <= 50]  # Max reasonable points
        if 'rebounds' in df.columns:
            df = df[df['rebounds'] <= 25]  # Max reasonable rebounds
        if 'assists' in df.columns:
            df = df[df['assists'] <= 20]  # Max reasonable assists
        
        final_rows = len(df)
        logger.info(f"🧹 Cleaned dataset: {initial_rows:,} → {final_rows:,} rows ({final_rows/initial_rows*100:.1f}% retained)")
        
        return df
    
    def save_expert_dataset(self, filename: str = "wnba_expert_dataset_129k.csv") -> str:
        """💾 Save the expert dataset"""
        if self.consolidated_df is None:
            raise ValueError("No consolidated dataset to save. Run consolidate_datasets() first.")
        
        output_path = Path(filename)
        self.consolidated_df.to_csv(output_path, index=False)
        
        size_mb = output_path.stat().st_size / 1024 / 1024
        logger.info(f"💾 Expert dataset saved: {filename} ({size_mb:.1f} MB)")
        
        # Save metadata
        metadata = {
            'created_at': datetime.now().isoformat(),
            'total_rows': len(self.consolidated_df),
            'total_columns': len(self.consolidated_df.columns),
            'data_sources': self.data_sources,
            'feature_columns': list(self.consolidated_df.columns),
            'size_mb': size_mb
        }
        
        metadata_path = output_path.with_suffix('.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        return str(output_path)

def main():
    """🎯 Main consolidation process"""
    print("🎯 EXPERT WNBA DATASET CONSOLIDATION")
    print("=" * 45)
    
    consolidator = ExpertDatasetConsolidator()
    
    # Discover data sources
    sources = consolidator.discover_data_sources()
    
    if not sources:
        print("❌ No data sources found!")
        return
    
    print(f"\n📊 Found {len(sources)} data sources:")
    for i, source in enumerate(sources, 1):
        print(f"   {i}. {Path(source['path']).name} ({source['size_mb']:.1f} MB, Priority: {source['priority']:.1f})")
    
    # Consolidate datasets
    print(f"\n🔄 Consolidating datasets...")
    dataset = consolidator.consolidate_datasets()
    
    # Save expert dataset
    output_file = consolidator.save_expert_dataset()
    
    print(f"\n🎯 CONSOLIDATION COMPLETE!")
    print(f"   • Final dataset: {len(dataset):,} rows × {len(dataset.columns)} columns")
    print(f"   • Output file: {output_file}")
    print(f"   • Ready for 46-model training system!")

if __name__ == "__main__":
    main()
