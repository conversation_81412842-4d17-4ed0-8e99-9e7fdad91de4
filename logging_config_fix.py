#!/usr/bin/env python3
"""
🔧 LOGGING CONFIGURATION FIX
============================

Fix Unicode encoding errors in Windows terminal by configuring proper logging.
"""

import logging
import sys
import os
from pathlib import Path

def fix_logging_encoding():
    """Fix Unicode encoding issues in logging"""
    
    # Set UTF-8 encoding for stdout/stderr
    if sys.platform == 'win32':
        # For Windows, set console to UTF-8
        try:
            os.system('chcp 65001 > nul')  # Set console to UTF-8
        except:
            pass
    
    # Configure logging with safe formatting
    class SafeFormatter(logging.Formatter):
        """Formatter that handles Unicode characters safely"""
        
        def format(self, record):
            # Replace problematic Unicode characters with safe alternatives
            msg = super().format(record)
            
            # Replace common emoji with text equivalents
            emoji_replacements = {
                '📡': '[COMM]',
                '⚡': '[EXEC]',
                '🎯': '[TARGET]',
                '✅': '[OK]',
                '❌': '[ERROR]',
                '🔧': '[REPAIR]',
                '🏀': '[WNBA]',
                '📊': '[DATA]',
                '🔍': '[SEARCH]',
                '📤': '[SEND]',
                '📅': '[DATE]',
                '🔴': '[LIVE]',
                '🎖️': '[EXPERT]',
                '🧠': '[AI]',
                '💾': '[SAVE]',
                '🚀': '[LAUNCH]',
                '⚠️': '[WARN]',
                '🔄': '[PROCESS]',
                '📈': '[STATS]',
                '💰': '[BET]',
                '🌐': '[NET]',
                '🎮': '[GAME]',
                '📋': '[LIST]',
                '🔬': '[TEST]',
                '⭐': '[STAR]',
                '🎪': '[SHOW]',
                '🎨': '[UI]',
                '🔒': '[SECURE]',
                '🔓': '[UNLOCK]',
                '📝': '[NOTE]',
                '🎵': '[AUDIO]',
                '📺': '[VIDEO]',
                '🖥️': '[SCREEN]',
                '📱': '[MOBILE]',
                '💻': '[LAPTOP]',
                '🖨️': '[PRINT]',
                '📷': '[CAMERA]',
                '🎥': '[RECORD]',
                '🔊': '[SOUND]',
                '🔇': '[MUTE]',
                '🔋': '[BATTERY]',
                '🔌': '[POWER]',
                '📶': '[SIGNAL]',
                '🛰️': '[SATELLITE]',
                '🌍': '[WORLD]',
                '🌎': '[EARTH]',
                '🌏': '[GLOBE]'
            }
            
            for emoji, replacement in emoji_replacements.items():
                msg = msg.replace(emoji, replacement)
            
            # Encode to ASCII, replacing problematic characters
            try:
                msg = msg.encode('ascii', 'replace').decode('ascii')
            except:
                pass
                
            return msg
    
    # Apply safe formatter to all loggers
    safe_formatter = SafeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Get root logger
    root_logger = logging.getLogger()
    
    # Update all handlers
    for handler in root_logger.handlers:
        handler.setFormatter(safe_formatter)
    
    # Also update specific loggers that might be problematic
    problematic_loggers = [
        'autopilot_model_communication_layer',
        'basketball_context_repair_system',
        'supreme_cache_management',
        'nba_api_connector',
        'real_wnba_data_fetcher',
        'win_probability_system',
        'central_cognitive_core'
    ]
    
    for logger_name in problematic_loggers:
        logger = logging.getLogger(logger_name)
        for handler in logger.handlers:
            handler.setFormatter(safe_formatter)
    
    print("[LOGGING] Unicode encoding fix applied successfully")

if __name__ == "__main__":
    fix_logging_encoding()
    print("[LOGGING] Logging configuration fixed - Unicode errors should be resolved")
