2025-07-15 12:32:34,468 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,247 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,593 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,593 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,594 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,594 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,058 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,074 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,075 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,090 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,317 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,344 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:54,593 - self_learning_error_resolver - INFO - Memory before cleanup: 94.4%
2025-07-15 12:32:56,360 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:58,390 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:06,189 - expert_odds_api_system - INFO -    Cache Duration: 30 minutes
2025-07-15 12:33:06,189 - expert_odds_api_system - INFO -    Daily Limit: 500 calls
2025-07-15 12:33:15,131 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,163 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,548 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,548 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,579 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,580 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,640 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,901 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:16,793 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:18,362 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:18,733 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:18,737 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:36,229 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:38,019 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:39,983 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:45,948 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:49,092 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:55,484 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:55,844 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:55,906 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:56,075 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:56,917 - self_learning_error_resolver - INFO - Memory before cleanup: 95.4%
2025-07-15 12:33:58,422 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:34:00,402 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:34:00,449 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:34:00,449 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:34:00,450 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 20:31:01,878 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:31:31,687 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:31:31,835 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CAC863BB0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/execute/sync
2025-07-15 20:31:35,927 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CCFE379B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/execute/sync
2025-07-15 20:31:40,046 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004C380>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/execute/sync
2025-07-15 20:31:48,261 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CC3A49AE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:31:52,354 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004C9E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:31:56,461 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004D040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:04,653 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004E140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:08,749 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004E7A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:12,873 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CCFE378A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:21,084 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004E030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:25,198 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004C6B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:29,298 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004D260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:37,521 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004CF30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:41,633 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004D370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:45,728 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004F240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:53,911 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CCFE34160>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:57,999 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004F020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:33:02,096 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004EF10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:36:04,628 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:36:14,607 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:38:20,481 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8937F790>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f52f6963c7bc287b13c5d6b3df525d0f
2025-07-15 20:38:24,590 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF877067A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f52f6963c7bc287b13c5d6b3df525d0f
2025-07-15 20:38:28,710 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF896445A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f52f6963c7bc287b13c5d6b3df525d0f
2025-07-15 20:38:36,894 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF89645370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/454ceeca844e8bd379b3456dab6d161a
2025-07-15 20:38:41,015 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF896456A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/454ceeca844e8bd379b3456dab6d161a
2025-07-15 20:38:45,114 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF89645E10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/454ceeca844e8bd379b3456dab6d161a
2025-07-15 20:38:53,330 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF896468B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e386e8db930b181dfba3f3fcd2fecfd5
2025-07-15 20:38:57,436 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF877069C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e386e8db930b181dfba3f3fcd2fecfd5
2025-07-15 20:39:01,524 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8937FCE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e386e8db930b181dfba3f3fcd2fecfd5
2025-07-15 20:44:45,405 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:44:45,808 - expert_odds_api_system - INFO -    Cache Duration: 30 minutes
2025-07-15 20:44:45,809 - expert_odds_api_system - INFO -    Daily Limit: 500 calls
2025-07-15 20:45:09,460 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_204509.json
2025-07-15 20:45:09,472 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_204509.csv
2025-07-15 20:45:14,143 - advanced_wnba_enhancements - INFO -    Performance gain: 12.8x faster
2025-07-15 20:45:14,150 - advanced_wnba_enhancements - INFO -    Digital twins: 0 WNBA scenarios
2025-07-15 20:45:14,180 - advanced_wnba_enhancements - INFO -    Live features: 3 generated
2025-07-15 20:45:14,226 - advanced_wnba_enhancements - INFO -    Optimal batch size: 64 samples
2025-07-15 20:45:14,676 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:45:17,349 - advanced_wnba_enhancements - INFO -    Best strategy: 0.1% improvement
2025-07-15 20:45:17,454 - advanced_wnba_enhancements - INFO -    Tactical recommendations: 6 generated
2025-07-15 20:45:17,465 - advanced_wnba_enhancements - INFO -    Player dynamics: 3 relationships modeled
2025-07-15 20:45:17,518 - advanced_wnba_enhancements - INFO -    Cost optimization: $74.13407890161575 (auto-approved)
2025-07-15 20:45:19,112 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:45:41,475 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_204541.json
2025-07-15 20:45:41,483 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_204541.csv
2025-07-15 20:46:09,379 - supreme_autopilot_system - WARNING -    federated_server: Poor basketball context integration
2025-07-15 20:46:09,379 - supreme_autopilot_system - WARNING -    federated_team_atl: Poor basketball context integration
2025-07-15 20:46:09,379 - supreme_autopilot_system - WARNING -    federated_team_chi: Poor basketball context integration
2025-07-15 20:46:09,380 - supreme_autopilot_system - WARNING -    federated_team_con: Poor basketball context integration
2025-07-15 20:46:09,380 - supreme_autopilot_system - WARNING -    federated_team_dal: Poor basketball context integration
2025-07-15 20:46:09,381 - supreme_autopilot_system - WARNING -    federated_team_gsv: Poor basketball context integration
2025-07-15 20:46:09,381 - supreme_autopilot_system - WARNING -    federated_team_ind: Poor basketball context integration
2025-07-15 20:46:09,382 - supreme_autopilot_system - WARNING -    federated_team_las: Poor basketball context integration
2025-07-15 20:46:09,382 - supreme_autopilot_system - WARNING -    federated_team_lv: Poor basketball context integration
2025-07-15 20:46:09,383 - supreme_autopilot_system - WARNING -    federated_team_min: Poor basketball context integration
2025-07-15 20:46:09,384 - supreme_autopilot_system - WARNING -    federated_team_nyl: Poor basketball context integration
2025-07-15 20:46:09,385 - supreme_autopilot_system - WARNING -    federated_team_pho: Poor basketball context integration
2025-07-15 20:46:09,385 - supreme_autopilot_system - WARNING -    federated_team_sea: Poor basketball context integration
2025-07-15 20:46:09,385 - supreme_autopilot_system - WARNING -    federated_team_was: Poor basketball context integration
2025-07-15 20:46:09,386 - supreme_autopilot_system - WARNING -    possession_based_model: Poor basketball context integration
2025-07-15 20:46:09,387 - supreme_autopilot_system - WARNING -    lineup_chemistry_model: Poor basketball context integration
2025-07-15 20:46:09,388 - supreme_autopilot_system - WARNING -    arena_effect_model: Poor basketball context integration
2025-07-15 20:46:09,397 - supreme_autopilot_system - WARNING -    team_dynamics_model: Poor basketball context integration
2025-07-15 20:46:09,403 - supreme_autopilot_system - WARNING -    contextual_performance_model: Poor basketball context integration
2025-07-15 20:46:09,404 - supreme_autopilot_system - WARNING -    federated_multiverse_ensemble: Poor basketball context integration
2025-07-15 20:46:09,404 - supreme_autopilot_system - WARNING -    player_rebounds_model: Poor basketball context integration
2025-07-15 20:46:09,405 - supreme_autopilot_system - WARNING -    player_assists_model: Poor basketball context integration
2025-07-15 20:46:09,405 - supreme_autopilot_system - WARNING -    player_threes_model: Poor basketball context integration
2025-07-15 20:46:09,406 - supreme_autopilot_system - WARNING -    player_double_double_model: Poor basketball context integration
2025-07-15 20:46:09,406 - supreme_autopilot_system - WARNING -    pregame_win_probability_model: Poor basketball context integration
2025-07-15 20:46:09,406 - supreme_autopilot_system - WARNING -    live_win_probability_model: Poor basketball context integration
2025-07-15 20:46:09,407 - supreme_autopilot_system - WARNING -    enhanced_player_points_model: Poor basketball context integration
2025-07-15 20:46:09,407 - supreme_autopilot_system - WARNING -    hybrid_gnn_model: Poor basketball context integration
2025-07-15 20:46:09,408 - supreme_autopilot_system - WARNING -    multitask_model: Poor basketball context integration
2025-07-15 20:46:09,409 - supreme_autopilot_system - WARNING -    bayesian_model: Poor basketball context integration
2025-07-15 20:46:09,409 - supreme_autopilot_system - WARNING -    upset_prediction_model: Poor basketball context integration
2025-07-15 20:46:09,409 - supreme_autopilot_system - WARNING -    injury_impact_model: Poor basketball context integration
2025-07-15 20:46:09,411 - supreme_autopilot_system - WARNING -    momentum_analysis_model: Poor basketball context integration
2025-07-15 20:46:09,412 - supreme_autopilot_system - WARNING -    real_time_betting_intelligence: Poor basketball context integration
2025-07-15 20:46:09,413 - supreme_autopilot_system - WARNING -    win_probability_system: Poor basketball context integration
2025-07-15 20:46:09,414 - supreme_autopilot_system - WARNING -    live_espn_nba_api_integration: Poor basketball context integration
2025-07-15 20:46:09,415 - supreme_autopilot_system - WARNING -    live_nba_api_system: Poor basketball context integration
2025-07-15 20:46:09,416 - supreme_autopilot_system - WARNING -    live_espn_system: Poor basketball context integration
2025-07-15 20:46:09,417 - supreme_autopilot_system - WARNING -    hybrid_live_data_system: Poor basketball context integration
2025-07-15 20:46:09,418 - supreme_autopilot_system - WARNING -    elite_prediction_dashboard: Poor basketball context integration
2025-07-15 20:46:09,418 - supreme_autopilot_system - WARNING -    wnba_war_room_system: Poor basketball context integration
2025-07-15 20:46:09,419 - supreme_autopilot_system - WARNING -    monitoring_web_dashboard: Poor basketball context integration
2025-07-15 20:46:09,420 - supreme_autopilot_system - WARNING -    supreme_autopilot_system: Poor basketball context integration
2025-07-15 20:46:09,421 - supreme_autopilot_system - WARNING -    medusa_autopilot: Poor basketball context integration
2025-07-15 20:46:09,422 - supreme_autopilot_system - WARNING -    model_performance_autopilot: Poor basketball context integration
2025-07-15 20:46:09,423 - supreme_autopilot_system - WARNING -    system_health_monitor: Poor basketball context integration
2025-07-15 20:46:58,011 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8BA448D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d63aa749642250ad2459b86e3a900104
2025-07-15 20:47:02,086 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC0D10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d63aa749642250ad2459b86e3a900104
2025-07-15 20:47:06,230 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC2BE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d63aa749642250ad2459b86e3a900104
2025-07-15 20:47:10,295 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_204710.json
2025-07-15 20:47:10,296 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_204710.csv
2025-07-15 20:47:14,530 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3b2ecc1c4804333b24e1e0e33bd9037e
2025-07-15 20:47:18,622 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3b2ecc1c4804333b24e1e0e33bd9037e
2025-07-15 20:47:22,741 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8BA448D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3b2ecc1c4804333b24e1e0e33bd9037e
2025-07-15 20:47:30,096 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8BA45150>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/execute/sync
2025-07-15 20:47:30,966 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC16A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/76b1f5f71bb310740f4db33b31dab04c
2025-07-15 20:47:34,192 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3AC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/execute/sync
2025-07-15 20:47:35,063 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3CE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/76b1f5f71bb310740f4db33b31dab04c
2025-07-15 20:47:38,291 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC1590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/execute/sync
2025-07-15 20:47:39,177 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC38A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/76b1f5f71bb310740f4db33b31dab04c
2025-07-15 20:47:46,545 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC1F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:47:50,654 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AFFCA685A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:47:54,749 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:02,991 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3F00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:07,101 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AFFCA68490>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:11,190 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC0D10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:14,237 - advanced_wnba_enhancements - INFO -    Performance gain: 12.5x faster
2025-07-15 20:48:14,241 - advanced_wnba_enhancements - INFO -    Live features: 3 generated
2025-07-15 20:48:14,254 - advanced_wnba_enhancements - INFO -    Optimal batch size: 64 samples
2025-07-15 20:48:19,398 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC2250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:23,514 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8BA448D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:27,620 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC1D00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:34,991 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_204834.json
2025-07-15 20:48:34,991 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_204834.csv
2025-07-15 20:48:35,845 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC1590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:39,966 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3460>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:44,059 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AFFCA69260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:52,246 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AFFCA69BF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:56,342 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AFFCA6A360>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:49:00,435 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC39B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:49:08,614 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC2140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:49:12,719 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC19D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:49:16,820 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC0F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:49:18,530 - advanced_wnba_enhancements - INFO -    Best strategy: 0.1% improvement
2025-07-15 20:49:18,532 - advanced_wnba_enhancements - INFO -    Tactical recommendations: 6 generated
2025-07-15 20:49:18,534 - advanced_wnba_enhancements - INFO -    Player dynamics: 3 relationships modeled
2025-07-15 20:49:18,535 - advanced_wnba_enhancements - INFO -    Cost optimization: $86.46181941250039 (auto-approved)
2025-07-15 20:49:25,056 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC17B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec
2025-07-15 20:49:29,160 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3CE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec
2025-07-15 20:49:33,257 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC1E10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec
2025-07-15 20:49:42,627 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC1BF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e1c51bcee8ffa9c0506e1c6e54b599ae
2025-07-15 20:49:46,695 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3CE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e1c51bcee8ffa9c0506e1c6e54b599ae
2025-07-15 20:49:47,205 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_204947.json
2025-07-15 20:49:47,205 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_204947.csv
2025-07-15 20:49:50,902 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3F00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e1c51bcee8ffa9c0506e1c6e54b599ae
2025-07-15 20:49:59,137 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8E5BC160>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a67300d5d882667cc8934b41e2dc4ab8
2025-07-15 20:50:03,244 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8E5BCD10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a67300d5d882667cc8934b41e2dc4ab8
2025-07-15 20:50:07,341 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8BA44AF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a67300d5d882667cc8934b41e2dc4ab8
2025-07-15 20:50:15,496 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec
2025-07-15 20:50:19,605 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8937F9B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec
2025-07-15 20:50:23,728 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8BA45150>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec
2025-07-15 20:51:07,881 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_205107.json
2025-07-15 20:51:07,882 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_205107.csv
2025-07-15 20:51:14,256 - advanced_wnba_enhancements - INFO -    Performance gain: 10.0x faster
2025-07-15 20:51:14,258 - advanced_wnba_enhancements - INFO -    Live features: 3 generated
2025-07-15 20:51:14,265 - advanced_wnba_enhancements - INFO -    Optimal batch size: 64 samples
2025-07-15 20:52:03,647 - supreme_autopilot_system - WARNING -    federated_server: Poor basketball context integration
2025-07-15 20:52:03,648 - supreme_autopilot_system - WARNING -    federated_team_atl: Poor basketball context integration
2025-07-15 20:52:03,648 - supreme_autopilot_system - WARNING -    federated_team_chi: Poor basketball context integration
2025-07-15 20:52:03,648 - supreme_autopilot_system - WARNING -    federated_team_con: Poor basketball context integration
2025-07-15 20:52:03,648 - supreme_autopilot_system - WARNING -    federated_team_dal: Poor basketball context integration
2025-07-15 20:52:03,648 - supreme_autopilot_system - WARNING -    federated_team_gsv: Poor basketball context integration
2025-07-15 20:52:03,648 - supreme_autopilot_system - WARNING -    federated_team_ind: Poor basketball context integration
2025-07-15 20:52:03,649 - supreme_autopilot_system - WARNING -    federated_team_las: Poor basketball context integration
2025-07-15 20:52:03,649 - supreme_autopilot_system - WARNING -    federated_team_lv: Poor basketball context integration
2025-07-15 20:52:03,649 - supreme_autopilot_system - WARNING -    federated_team_min: Poor basketball context integration
2025-07-15 20:52:03,649 - supreme_autopilot_system - WARNING -    federated_team_nyl: Poor basketball context integration
2025-07-15 20:52:03,649 - supreme_autopilot_system - WARNING -    federated_team_pho: Poor basketball context integration
2025-07-15 20:52:03,649 - supreme_autopilot_system - WARNING -    federated_team_sea: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    federated_team_was: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    possession_based_model: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    lineup_chemistry_model: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    arena_effect_model: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    team_dynamics_model: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    contextual_performance_model: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    federated_multiverse_ensemble: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    player_rebounds_model: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    player_assists_model: Poor basketball context integration
2025-07-15 20:52:03,651 - supreme_autopilot_system - WARNING -    player_threes_model: Poor basketball context integration
2025-07-15 20:52:03,651 - supreme_autopilot_system - WARNING -    player_double_double_model: Poor basketball context integration
2025-07-15 20:52:03,651 - supreme_autopilot_system - WARNING -    pregame_win_probability_model: Poor basketball context integration
2025-07-15 20:52:03,651 - supreme_autopilot_system - WARNING -    live_win_probability_model: Poor basketball context integration
2025-07-15 20:52:03,651 - supreme_autopilot_system - WARNING -    enhanced_player_points_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    hybrid_gnn_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    multitask_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    bayesian_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    upset_prediction_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    injury_impact_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    momentum_analysis_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    real_time_betting_intelligence: Poor basketball context integration
2025-07-15 20:52:03,653 - supreme_autopilot_system - WARNING -    win_probability_system: Poor basketball context integration
2025-07-15 20:52:03,653 - supreme_autopilot_system - WARNING -    live_espn_nba_api_integration: Poor basketball context integration
2025-07-15 20:52:03,653 - supreme_autopilot_system - WARNING -    live_nba_api_system: Poor basketball context integration
2025-07-15 20:52:03,653 - supreme_autopilot_system - WARNING -    live_espn_system: Poor basketball context integration
2025-07-15 20:52:03,653 - supreme_autopilot_system - WARNING -    hybrid_live_data_system: Poor basketball context integration
2025-07-15 20:52:03,653 - supreme_autopilot_system - WARNING -    elite_prediction_dashboard: Poor basketball context integration
2025-07-15 20:52:03,654 - supreme_autopilot_system - WARNING -    wnba_war_room_system: Poor basketball context integration
2025-07-15 20:52:03,654 - supreme_autopilot_system - WARNING -    monitoring_web_dashboard: Poor basketball context integration
2025-07-15 20:52:03,654 - supreme_autopilot_system - WARNING -    supreme_autopilot_system: Poor basketball context integration
2025-07-15 20:52:03,654 - supreme_autopilot_system - WARNING -    medusa_autopilot: Poor basketball context integration
2025-07-15 20:52:03,654 - supreme_autopilot_system - WARNING -    model_performance_autopilot: Poor basketball context integration
2025-07-15 20:52:03,655 - supreme_autopilot_system - WARNING -    system_health_monitor: Poor basketball context integration
2025-07-15 20:52:31,225 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_205231.json
2025-07-15 20:52:31,226 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_205231.csv
2025-07-15 20:53:19,552 - advanced_wnba_enhancements - INFO -    Best strategy: 0.1% improvement
2025-07-15 20:53:19,557 - advanced_wnba_enhancements - INFO -    Tactical recommendations: 6 generated
2025-07-15 20:53:19,564 - advanced_wnba_enhancements - INFO -    Player dynamics: 3 relationships modeled
2025-07-15 20:53:19,571 - advanced_wnba_enhancements - INFO -    Cost optimization: $60.6007916896939 (auto-approved)
2025-07-15 20:53:52,300 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_205352.json
2025-07-15 20:53:52,301 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_205352.csv
2025-07-15 20:53:55,182 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:54:14,298 - advanced_wnba_enhancements - INFO -    Performance gain: 9.4x faster
2025-07-15 20:54:14,342 - advanced_wnba_enhancements - INFO -    Live features: 3 generated
2025-07-15 20:54:14,378 - advanced_wnba_enhancements - INFO -    Optimal batch size: 64 samples
2025-07-15 20:54:18,725 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_205418.json
2025-07-15 20:54:18,725 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_205418.csv
2025-07-15 20:54:23,273 - advanced_wnba_enhancements - INFO -    Performance gain: 9.7x faster
2025-07-15 20:54:23,278 - advanced_wnba_enhancements - INFO -    Digital twins: 0 WNBA scenarios
2025-07-15 20:54:23,307 - advanced_wnba_enhancements - INFO -    Live features: 3 generated
2025-07-15 20:54:23,351 - advanced_wnba_enhancements - INFO -    Optimal batch size: 64 samples
2025-07-15 20:54:24,066 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:54:24,304 - advanced_wnba_enhancements - INFO -    Best strategy: 0.1% improvement
2025-07-15 20:54:24,390 - advanced_wnba_enhancements - INFO -    Tactical recommendations: 6 generated
2025-07-15 20:54:24,483 - advanced_wnba_enhancements - INFO -    Player dynamics: 3 relationships modeled
2025-07-15 20:54:24,500 - advanced_wnba_enhancements - INFO -    Cost optimization: $73.85022584349542 (auto-approved)
2025-07-15 20:54:28,651 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:54:50,608 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_205450.json
2025-07-15 20:54:50,609 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_205450.csv
2025-07-15 20:55:15,081 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_205514.json
2025-07-15 20:55:15,091 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_205514.csv
2025-07-15 20:55:23,274 - supreme_autopilot_system - WARNING -    federated_server: Poor basketball context integration
2025-07-15 20:55:23,275 - supreme_autopilot_system - WARNING -    federated_team_atl: Poor basketball context integration
2025-07-15 20:55:23,275 - supreme_autopilot_system - WARNING -    federated_team_chi: Poor basketball context integration
2025-07-15 20:55:23,275 - supreme_autopilot_system - WARNING -    federated_team_con: Poor basketball context integration
2025-07-15 20:55:23,275 - supreme_autopilot_system - WARNING -    federated_team_dal: Poor basketball context integration
2025-07-15 20:55:23,276 - supreme_autopilot_system - WARNING -    federated_team_gsv: Poor basketball context integration
2025-07-15 20:55:23,276 - supreme_autopilot_system - WARNING -    federated_team_ind: Poor basketball context integration
2025-07-15 20:55:23,277 - supreme_autopilot_system - WARNING -    federated_team_las: Poor basketball context integration
2025-07-15 20:55:23,277 - supreme_autopilot_system - WARNING -    federated_team_lv: Poor basketball context integration
2025-07-15 20:55:23,278 - supreme_autopilot_system - WARNING -    federated_team_min: Poor basketball context integration
2025-07-15 20:55:23,278 - supreme_autopilot_system - WARNING -    federated_team_nyl: Poor basketball context integration
2025-07-15 20:55:23,278 - supreme_autopilot_system - WARNING -    federated_team_pho: Poor basketball context integration
2025-07-15 20:55:23,279 - supreme_autopilot_system - WARNING -    federated_team_sea: Poor basketball context integration
2025-07-15 20:55:23,279 - supreme_autopilot_system - WARNING -    federated_team_was: Poor basketball context integration
2025-07-15 20:55:23,279 - supreme_autopilot_system - WARNING -    possession_based_model: Poor basketball context integration
2025-07-15 20:55:23,279 - supreme_autopilot_system - WARNING -    lineup_chemistry_model: Poor basketball context integration
2025-07-15 20:55:23,280 - supreme_autopilot_system - WARNING -    arena_effect_model: Poor basketball context integration
2025-07-15 20:55:23,280 - supreme_autopilot_system - WARNING -    team_dynamics_model: Poor basketball context integration
2025-07-15 20:55:23,280 - supreme_autopilot_system - WARNING -    contextual_performance_model: Poor basketball context integration
2025-07-15 20:55:23,280 - supreme_autopilot_system - WARNING -    federated_multiverse_ensemble: Poor basketball context integration
2025-07-15 20:55:23,281 - supreme_autopilot_system - WARNING -    player_rebounds_model: Poor basketball context integration
2025-07-15 20:55:23,281 - supreme_autopilot_system - WARNING -    player_assists_model: Poor basketball context integration
2025-07-15 20:55:23,281 - supreme_autopilot_system - WARNING -    player_threes_model: Poor basketball context integration
2025-07-15 20:55:23,282 - supreme_autopilot_system - WARNING -    player_double_double_model: Poor basketball context integration
2025-07-15 20:55:23,282 - supreme_autopilot_system - WARNING -    pregame_win_probability_model: Poor basketball context integration
2025-07-15 20:55:23,282 - supreme_autopilot_system - WARNING -    live_win_probability_model: Poor basketball context integration
2025-07-15 20:55:23,282 - supreme_autopilot_system - WARNING -    enhanced_player_points_model: Poor basketball context integration
2025-07-15 20:55:23,283 - supreme_autopilot_system - WARNING -    hybrid_gnn_model: Poor basketball context integration
2025-07-15 20:55:23,283 - supreme_autopilot_system - WARNING -    multitask_model: Poor basketball context integration
2025-07-15 20:55:23,283 - supreme_autopilot_system - WARNING -    bayesian_model: Poor basketball context integration
2025-07-15 20:55:23,284 - supreme_autopilot_system - WARNING -    upset_prediction_model: Poor basketball context integration
2025-07-15 20:55:23,284 - supreme_autopilot_system - WARNING -    injury_impact_model: Poor basketball context integration
2025-07-15 20:55:23,285 - supreme_autopilot_system - WARNING -    momentum_analysis_model: Poor basketball context integration
2025-07-15 20:55:23,285 - supreme_autopilot_system - WARNING -    real_time_betting_intelligence: Poor basketball context integration
2025-07-15 20:55:23,285 - supreme_autopilot_system - WARNING -    win_probability_system: Poor basketball context integration
2025-07-15 20:55:23,285 - supreme_autopilot_system - WARNING -    live_espn_nba_api_integration: Poor basketball context integration
2025-07-15 20:55:23,286 - supreme_autopilot_system - WARNING -    live_nba_api_system: Poor basketball context integration
2025-07-15 20:55:23,286 - supreme_autopilot_system - WARNING -    live_espn_system: Poor basketball context integration
2025-07-15 20:55:23,286 - supreme_autopilot_system - WARNING -    hybrid_live_data_system: Poor basketball context integration
2025-07-15 20:55:23,286 - supreme_autopilot_system - WARNING -    elite_prediction_dashboard: Poor basketball context integration
2025-07-15 20:55:23,286 - supreme_autopilot_system - WARNING -    wnba_war_room_system: Poor basketball context integration
2025-07-15 20:55:23,287 - supreme_autopilot_system - WARNING -    monitoring_web_dashboard: Poor basketball context integration
2025-07-15 20:55:23,287 - supreme_autopilot_system - WARNING -    supreme_autopilot_system: Poor basketball context integration
2025-07-15 20:55:23,287 - supreme_autopilot_system - WARNING -    medusa_autopilot: Poor basketball context integration
2025-07-15 20:55:23,287 - supreme_autopilot_system - WARNING -    model_performance_autopilot: Poor basketball context integration
2025-07-15 20:55:23,287 - supreme_autopilot_system - WARNING -    system_health_monitor: Poor basketball context integration
2025-07-15 20:56:07,743 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/745aaf62cf453ecb85def61cd57639ec
