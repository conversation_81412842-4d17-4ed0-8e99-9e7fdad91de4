2025-07-15 12:32:34,468 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,247 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,593 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,593 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,594 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,594 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,058 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,074 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,075 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,090 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,317 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,344 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:54,593 - self_learning_error_resolver - INFO - Memory before cleanup: 94.4%
2025-07-15 12:32:56,360 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:58,390 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:06,189 - expert_odds_api_system - INFO -    Cache Duration: 30 minutes
2025-07-15 12:33:06,189 - expert_odds_api_system - INFO -    Daily Limit: 500 calls
2025-07-15 12:33:15,131 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,163 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,548 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,548 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,579 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,580 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,640 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,901 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:16,793 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:18,362 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:18,733 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:18,737 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:36,229 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:38,019 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:39,983 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:45,948 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:49,092 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:55,484 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:55,844 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:55,906 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:56,075 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:56,917 - self_learning_error_resolver - INFO - Memory before cleanup: 95.4%
2025-07-15 12:33:58,422 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:34:00,402 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:34:00,449 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:34:00,449 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:34:00,450 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 20:31:01,878 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:31:31,687 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:31:31,835 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CAC863BB0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/execute/sync
2025-07-15 20:31:35,927 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CCFE379B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/execute/sync
2025-07-15 20:31:40,046 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004C380>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/execute/sync
2025-07-15 20:31:48,261 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CC3A49AE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:31:52,354 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004C9E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:31:56,461 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004D040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:04,653 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004E140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:08,749 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004E7A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:12,873 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CCFE378A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:21,084 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004E030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:25,198 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004C6B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:29,298 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004D260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:37,521 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004CF30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:41,633 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004D370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:45,728 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004F240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:53,911 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CCFE34160>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:57,999 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004F020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:33:02,096 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004EF10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
