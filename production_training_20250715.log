2025-07-15 12:32:34,468 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,247 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,593 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,593 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,594 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:35,594 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,058 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,074 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,075 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,090 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,317 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:38,344 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:54,593 - self_learning_error_resolver - INFO - Memory before cleanup: 94.4%
2025-07-15 12:32:56,360 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:32:58,390 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:06,189 - expert_odds_api_system - INFO -    Cache Duration: 30 minutes
2025-07-15 12:33:06,189 - expert_odds_api_system - INFO -    Daily Limit: 500 calls
2025-07-15 12:33:15,131 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,163 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,548 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,548 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,579 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,580 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,640 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:15,901 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:16,793 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:18,362 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:18,733 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:18,737 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:36,229 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:38,019 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:39,983 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:45,948 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:49,092 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:55,484 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:55,844 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:55,906 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:56,075 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:33:56,917 - self_learning_error_resolver - INFO - Memory before cleanup: 95.4%
2025-07-15 12:33:58,422 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:34:00,402 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:34:00,449 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:34:00,449 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 12:34:00,450 - nba_api_connector - ERROR - NBA API timeout for scoreboardV2
2025-07-15 20:31:01,878 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:31:31,687 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:31:31,835 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CAC863BB0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/execute/sync
2025-07-15 20:31:35,927 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CCFE379B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/execute/sync
2025-07-15 20:31:40,046 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004C380>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/execute/sync
2025-07-15 20:31:48,261 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CC3A49AE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:31:52,354 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004C9E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:31:56,461 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004D040>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:04,653 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004E140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:08,749 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004E7A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:12,873 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CCFE378A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:21,084 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004E030>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:25,198 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004C6B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:29,298 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004D260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:37,521 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004CF30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:41,633 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004D370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:45,728 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004F240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:53,911 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CCFE34160>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:32:57,999 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004F020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:33:02,096 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017CD004EF10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4a0f9b11cc2c3df4bcf3d593ecea5704/elements
2025-07-15 20:36:04,628 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:36:14,607 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:38:20,481 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8937F790>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f52f6963c7bc287b13c5d6b3df525d0f
2025-07-15 20:38:24,590 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF877067A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f52f6963c7bc287b13c5d6b3df525d0f
2025-07-15 20:38:28,710 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF896445A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/f52f6963c7bc287b13c5d6b3df525d0f
2025-07-15 20:38:36,894 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF89645370>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/454ceeca844e8bd379b3456dab6d161a
2025-07-15 20:38:41,015 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF896456A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/454ceeca844e8bd379b3456dab6d161a
2025-07-15 20:38:45,114 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF89645E10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/454ceeca844e8bd379b3456dab6d161a
2025-07-15 20:38:53,330 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF896468B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e386e8db930b181dfba3f3fcd2fecfd5
2025-07-15 20:38:57,436 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF877069C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e386e8db930b181dfba3f3fcd2fecfd5
2025-07-15 20:39:01,524 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8937FCE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e386e8db930b181dfba3f3fcd2fecfd5
2025-07-15 20:44:45,405 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:44:45,808 - expert_odds_api_system - INFO -    Cache Duration: 30 minutes
2025-07-15 20:44:45,809 - expert_odds_api_system - INFO -    Daily Limit: 500 calls
2025-07-15 20:45:09,460 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_204509.json
2025-07-15 20:45:09,472 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_204509.csv
2025-07-15 20:45:14,143 - advanced_wnba_enhancements - INFO -    Performance gain: 12.8x faster
2025-07-15 20:45:14,150 - advanced_wnba_enhancements - INFO -    Digital twins: 0 WNBA scenarios
2025-07-15 20:45:14,180 - advanced_wnba_enhancements - INFO -    Live features: 3 generated
2025-07-15 20:45:14,226 - advanced_wnba_enhancements - INFO -    Optimal batch size: 64 samples
2025-07-15 20:45:14,676 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:45:17,349 - advanced_wnba_enhancements - INFO -    Best strategy: 0.1% improvement
2025-07-15 20:45:17,454 - advanced_wnba_enhancements - INFO -    Tactical recommendations: 6 generated
2025-07-15 20:45:17,465 - advanced_wnba_enhancements - INFO -    Player dynamics: 3 relationships modeled
2025-07-15 20:45:17,518 - advanced_wnba_enhancements - INFO -    Cost optimization: $74.13407890161575 (auto-approved)
2025-07-15 20:45:19,112 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:45:41,475 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_204541.json
2025-07-15 20:45:41,483 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_204541.csv
2025-07-15 20:46:09,379 - supreme_autopilot_system - WARNING -    federated_server: Poor basketball context integration
2025-07-15 20:46:09,379 - supreme_autopilot_system - WARNING -    federated_team_atl: Poor basketball context integration
2025-07-15 20:46:09,379 - supreme_autopilot_system - WARNING -    federated_team_chi: Poor basketball context integration
2025-07-15 20:46:09,380 - supreme_autopilot_system - WARNING -    federated_team_con: Poor basketball context integration
2025-07-15 20:46:09,380 - supreme_autopilot_system - WARNING -    federated_team_dal: Poor basketball context integration
2025-07-15 20:46:09,381 - supreme_autopilot_system - WARNING -    federated_team_gsv: Poor basketball context integration
2025-07-15 20:46:09,381 - supreme_autopilot_system - WARNING -    federated_team_ind: Poor basketball context integration
2025-07-15 20:46:09,382 - supreme_autopilot_system - WARNING -    federated_team_las: Poor basketball context integration
2025-07-15 20:46:09,382 - supreme_autopilot_system - WARNING -    federated_team_lv: Poor basketball context integration
2025-07-15 20:46:09,383 - supreme_autopilot_system - WARNING -    federated_team_min: Poor basketball context integration
2025-07-15 20:46:09,384 - supreme_autopilot_system - WARNING -    federated_team_nyl: Poor basketball context integration
2025-07-15 20:46:09,385 - supreme_autopilot_system - WARNING -    federated_team_pho: Poor basketball context integration
2025-07-15 20:46:09,385 - supreme_autopilot_system - WARNING -    federated_team_sea: Poor basketball context integration
2025-07-15 20:46:09,385 - supreme_autopilot_system - WARNING -    federated_team_was: Poor basketball context integration
2025-07-15 20:46:09,386 - supreme_autopilot_system - WARNING -    possession_based_model: Poor basketball context integration
2025-07-15 20:46:09,387 - supreme_autopilot_system - WARNING -    lineup_chemistry_model: Poor basketball context integration
2025-07-15 20:46:09,388 - supreme_autopilot_system - WARNING -    arena_effect_model: Poor basketball context integration
2025-07-15 20:46:09,397 - supreme_autopilot_system - WARNING -    team_dynamics_model: Poor basketball context integration
2025-07-15 20:46:09,403 - supreme_autopilot_system - WARNING -    contextual_performance_model: Poor basketball context integration
2025-07-15 20:46:09,404 - supreme_autopilot_system - WARNING -    federated_multiverse_ensemble: Poor basketball context integration
2025-07-15 20:46:09,404 - supreme_autopilot_system - WARNING -    player_rebounds_model: Poor basketball context integration
2025-07-15 20:46:09,405 - supreme_autopilot_system - WARNING -    player_assists_model: Poor basketball context integration
2025-07-15 20:46:09,405 - supreme_autopilot_system - WARNING -    player_threes_model: Poor basketball context integration
2025-07-15 20:46:09,406 - supreme_autopilot_system - WARNING -    player_double_double_model: Poor basketball context integration
2025-07-15 20:46:09,406 - supreme_autopilot_system - WARNING -    pregame_win_probability_model: Poor basketball context integration
2025-07-15 20:46:09,406 - supreme_autopilot_system - WARNING -    live_win_probability_model: Poor basketball context integration
2025-07-15 20:46:09,407 - supreme_autopilot_system - WARNING -    enhanced_player_points_model: Poor basketball context integration
2025-07-15 20:46:09,407 - supreme_autopilot_system - WARNING -    hybrid_gnn_model: Poor basketball context integration
2025-07-15 20:46:09,408 - supreme_autopilot_system - WARNING -    multitask_model: Poor basketball context integration
2025-07-15 20:46:09,409 - supreme_autopilot_system - WARNING -    bayesian_model: Poor basketball context integration
2025-07-15 20:46:09,409 - supreme_autopilot_system - WARNING -    upset_prediction_model: Poor basketball context integration
2025-07-15 20:46:09,409 - supreme_autopilot_system - WARNING -    injury_impact_model: Poor basketball context integration
2025-07-15 20:46:09,411 - supreme_autopilot_system - WARNING -    momentum_analysis_model: Poor basketball context integration
2025-07-15 20:46:09,412 - supreme_autopilot_system - WARNING -    real_time_betting_intelligence: Poor basketball context integration
2025-07-15 20:46:09,413 - supreme_autopilot_system - WARNING -    win_probability_system: Poor basketball context integration
2025-07-15 20:46:09,414 - supreme_autopilot_system - WARNING -    live_espn_nba_api_integration: Poor basketball context integration
2025-07-15 20:46:09,415 - supreme_autopilot_system - WARNING -    live_nba_api_system: Poor basketball context integration
2025-07-15 20:46:09,416 - supreme_autopilot_system - WARNING -    live_espn_system: Poor basketball context integration
2025-07-15 20:46:09,417 - supreme_autopilot_system - WARNING -    hybrid_live_data_system: Poor basketball context integration
2025-07-15 20:46:09,418 - supreme_autopilot_system - WARNING -    elite_prediction_dashboard: Poor basketball context integration
2025-07-15 20:46:09,418 - supreme_autopilot_system - WARNING -    wnba_war_room_system: Poor basketball context integration
2025-07-15 20:46:09,419 - supreme_autopilot_system - WARNING -    monitoring_web_dashboard: Poor basketball context integration
2025-07-15 20:46:09,420 - supreme_autopilot_system - WARNING -    supreme_autopilot_system: Poor basketball context integration
2025-07-15 20:46:09,421 - supreme_autopilot_system - WARNING -    medusa_autopilot: Poor basketball context integration
2025-07-15 20:46:09,422 - supreme_autopilot_system - WARNING -    model_performance_autopilot: Poor basketball context integration
2025-07-15 20:46:09,423 - supreme_autopilot_system - WARNING -    system_health_monitor: Poor basketball context integration
2025-07-15 20:46:58,011 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8BA448D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d63aa749642250ad2459b86e3a900104
2025-07-15 20:47:02,086 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC0D10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d63aa749642250ad2459b86e3a900104
2025-07-15 20:47:06,230 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC2BE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/d63aa749642250ad2459b86e3a900104
2025-07-15 20:47:10,295 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_204710.json
2025-07-15 20:47:10,296 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_204710.csv
2025-07-15 20:47:14,530 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3b2ecc1c4804333b24e1e0e33bd9037e
2025-07-15 20:47:18,622 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3020>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3b2ecc1c4804333b24e1e0e33bd9037e
2025-07-15 20:47:22,741 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8BA448D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/3b2ecc1c4804333b24e1e0e33bd9037e
2025-07-15 20:47:30,096 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8BA45150>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/execute/sync
2025-07-15 20:47:30,966 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC16A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/76b1f5f71bb310740f4db33b31dab04c
2025-07-15 20:47:34,192 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3AC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/execute/sync
2025-07-15 20:47:35,063 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3CE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/76b1f5f71bb310740f4db33b31dab04c
2025-07-15 20:47:38,291 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC1590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/execute/sync
2025-07-15 20:47:39,177 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC38A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/76b1f5f71bb310740f4db33b31dab04c
2025-07-15 20:47:46,545 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC1F20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:47:50,654 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AFFCA685A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:47:54,749 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:02,991 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3F00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:07,101 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AFFCA68490>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:11,190 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC0D10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:14,237 - advanced_wnba_enhancements - INFO -    Performance gain: 12.5x faster
2025-07-15 20:48:14,241 - advanced_wnba_enhancements - INFO -    Live features: 3 generated
2025-07-15 20:48:14,254 - advanced_wnba_enhancements - INFO -    Optimal batch size: 64 samples
2025-07-15 20:48:19,398 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC2250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:23,514 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8BA448D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:27,620 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC1D00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:34,991 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_204834.json
2025-07-15 20:48:34,991 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_204834.csv
2025-07-15 20:48:35,845 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC1590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:39,966 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3460>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:44,059 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AFFCA69260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:52,246 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AFFCA69BF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:48:56,342 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AFFCA6A360>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:49:00,435 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC39B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:49:08,614 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC2140>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:49:12,719 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC19D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:49:16,820 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC0F30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec/elements
2025-07-15 20:49:18,530 - advanced_wnba_enhancements - INFO -    Best strategy: 0.1% improvement
2025-07-15 20:49:18,532 - advanced_wnba_enhancements - INFO -    Tactical recommendations: 6 generated
2025-07-15 20:49:18,534 - advanced_wnba_enhancements - INFO -    Player dynamics: 3 relationships modeled
2025-07-15 20:49:18,535 - advanced_wnba_enhancements - INFO -    Cost optimization: $86.46181941250039 (auto-approved)
2025-07-15 20:49:25,056 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC17B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec
2025-07-15 20:49:29,160 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3CE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec
2025-07-15 20:49:33,257 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC1E10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec
2025-07-15 20:49:42,627 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC1BF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e1c51bcee8ffa9c0506e1c6e54b599ae
2025-07-15 20:49:46,695 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3CE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e1c51bcee8ffa9c0506e1c6e54b599ae
2025-07-15 20:49:47,205 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_204947.json
2025-07-15 20:49:47,205 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_204947.csv
2025-07-15 20:49:50,902 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3F00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/e1c51bcee8ffa9c0506e1c6e54b599ae
2025-07-15 20:49:59,137 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8E5BC160>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a67300d5d882667cc8934b41e2dc4ab8
2025-07-15 20:50:03,244 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8E5BCD10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a67300d5d882667cc8934b41e2dc4ab8
2025-07-15 20:50:07,341 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8BA44AF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a67300d5d882667cc8934b41e2dc4ab8
2025-07-15 20:50:15,496 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8AEC3240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec
2025-07-15 20:50:19,605 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8937F9B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec
2025-07-15 20:50:23,728 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002AF8BA45150>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/a2d5fade5ebb8145be4d500caeeb70ec
2025-07-15 20:51:07,881 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_205107.json
2025-07-15 20:51:07,882 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_205107.csv
2025-07-15 20:51:14,256 - advanced_wnba_enhancements - INFO -    Performance gain: 10.0x faster
2025-07-15 20:51:14,258 - advanced_wnba_enhancements - INFO -    Live features: 3 generated
2025-07-15 20:51:14,265 - advanced_wnba_enhancements - INFO -    Optimal batch size: 64 samples
2025-07-15 20:52:03,647 - supreme_autopilot_system - WARNING -    federated_server: Poor basketball context integration
2025-07-15 20:52:03,648 - supreme_autopilot_system - WARNING -    federated_team_atl: Poor basketball context integration
2025-07-15 20:52:03,648 - supreme_autopilot_system - WARNING -    federated_team_chi: Poor basketball context integration
2025-07-15 20:52:03,648 - supreme_autopilot_system - WARNING -    federated_team_con: Poor basketball context integration
2025-07-15 20:52:03,648 - supreme_autopilot_system - WARNING -    federated_team_dal: Poor basketball context integration
2025-07-15 20:52:03,648 - supreme_autopilot_system - WARNING -    federated_team_gsv: Poor basketball context integration
2025-07-15 20:52:03,648 - supreme_autopilot_system - WARNING -    federated_team_ind: Poor basketball context integration
2025-07-15 20:52:03,649 - supreme_autopilot_system - WARNING -    federated_team_las: Poor basketball context integration
2025-07-15 20:52:03,649 - supreme_autopilot_system - WARNING -    federated_team_lv: Poor basketball context integration
2025-07-15 20:52:03,649 - supreme_autopilot_system - WARNING -    federated_team_min: Poor basketball context integration
2025-07-15 20:52:03,649 - supreme_autopilot_system - WARNING -    federated_team_nyl: Poor basketball context integration
2025-07-15 20:52:03,649 - supreme_autopilot_system - WARNING -    federated_team_pho: Poor basketball context integration
2025-07-15 20:52:03,649 - supreme_autopilot_system - WARNING -    federated_team_sea: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    federated_team_was: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    possession_based_model: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    lineup_chemistry_model: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    arena_effect_model: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    team_dynamics_model: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    contextual_performance_model: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    federated_multiverse_ensemble: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    player_rebounds_model: Poor basketball context integration
2025-07-15 20:52:03,650 - supreme_autopilot_system - WARNING -    player_assists_model: Poor basketball context integration
2025-07-15 20:52:03,651 - supreme_autopilot_system - WARNING -    player_threes_model: Poor basketball context integration
2025-07-15 20:52:03,651 - supreme_autopilot_system - WARNING -    player_double_double_model: Poor basketball context integration
2025-07-15 20:52:03,651 - supreme_autopilot_system - WARNING -    pregame_win_probability_model: Poor basketball context integration
2025-07-15 20:52:03,651 - supreme_autopilot_system - WARNING -    live_win_probability_model: Poor basketball context integration
2025-07-15 20:52:03,651 - supreme_autopilot_system - WARNING -    enhanced_player_points_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    hybrid_gnn_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    multitask_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    bayesian_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    upset_prediction_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    injury_impact_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    momentum_analysis_model: Poor basketball context integration
2025-07-15 20:52:03,652 - supreme_autopilot_system - WARNING -    real_time_betting_intelligence: Poor basketball context integration
2025-07-15 20:52:03,653 - supreme_autopilot_system - WARNING -    win_probability_system: Poor basketball context integration
2025-07-15 20:52:03,653 - supreme_autopilot_system - WARNING -    live_espn_nba_api_integration: Poor basketball context integration
2025-07-15 20:52:03,653 - supreme_autopilot_system - WARNING -    live_nba_api_system: Poor basketball context integration
2025-07-15 20:52:03,653 - supreme_autopilot_system - WARNING -    live_espn_system: Poor basketball context integration
2025-07-15 20:52:03,653 - supreme_autopilot_system - WARNING -    hybrid_live_data_system: Poor basketball context integration
2025-07-15 20:52:03,653 - supreme_autopilot_system - WARNING -    elite_prediction_dashboard: Poor basketball context integration
2025-07-15 20:52:03,654 - supreme_autopilot_system - WARNING -    wnba_war_room_system: Poor basketball context integration
2025-07-15 20:52:03,654 - supreme_autopilot_system - WARNING -    monitoring_web_dashboard: Poor basketball context integration
2025-07-15 20:52:03,654 - supreme_autopilot_system - WARNING -    supreme_autopilot_system: Poor basketball context integration
2025-07-15 20:52:03,654 - supreme_autopilot_system - WARNING -    medusa_autopilot: Poor basketball context integration
2025-07-15 20:52:03,654 - supreme_autopilot_system - WARNING -    model_performance_autopilot: Poor basketball context integration
2025-07-15 20:52:03,655 - supreme_autopilot_system - WARNING -    system_health_monitor: Poor basketball context integration
2025-07-15 20:52:31,225 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_205231.json
2025-07-15 20:52:31,226 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_205231.csv
2025-07-15 20:53:19,552 - advanced_wnba_enhancements - INFO -    Best strategy: 0.1% improvement
2025-07-15 20:53:19,557 - advanced_wnba_enhancements - INFO -    Tactical recommendations: 6 generated
2025-07-15 20:53:19,564 - advanced_wnba_enhancements - INFO -    Player dynamics: 3 relationships modeled
2025-07-15 20:53:19,571 - advanced_wnba_enhancements - INFO -    Cost optimization: $60.6007916896939 (auto-approved)
2025-07-15 20:53:52,300 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_205352.json
2025-07-15 20:53:52,301 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_205352.csv
2025-07-15 20:53:55,182 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:54:14,298 - advanced_wnba_enhancements - INFO -    Performance gain: 9.4x faster
2025-07-15 20:54:14,342 - advanced_wnba_enhancements - INFO -    Live features: 3 generated
2025-07-15 20:54:14,378 - advanced_wnba_enhancements - INFO -    Optimal batch size: 64 samples
2025-07-15 20:54:18,725 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_205418.json
2025-07-15 20:54:18,725 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_205418.csv
2025-07-15 20:54:23,273 - advanced_wnba_enhancements - INFO -    Performance gain: 9.7x faster
2025-07-15 20:54:23,278 - advanced_wnba_enhancements - INFO -    Digital twins: 0 WNBA scenarios
2025-07-15 20:54:23,307 - advanced_wnba_enhancements - INFO -    Live features: 3 generated
2025-07-15 20:54:23,351 - advanced_wnba_enhancements - INFO -    Optimal batch size: 64 samples
2025-07-15 20:54:24,066 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:54:24,304 - advanced_wnba_enhancements - INFO -    Best strategy: 0.1% improvement
2025-07-15 20:54:24,390 - advanced_wnba_enhancements - INFO -    Tactical recommendations: 6 generated
2025-07-15 20:54:24,483 - advanced_wnba_enhancements - INFO -    Player dynamics: 3 relationships modeled
2025-07-15 20:54:24,500 - advanced_wnba_enhancements - INFO -    Cost optimization: $73.85022584349542 (auto-approved)
2025-07-15 20:54:28,651 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:54:50,608 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_205450.json
2025-07-15 20:54:50,609 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_205450.csv
2025-07-15 20:55:15,081 - expert_mapping_integration - INFO -    JSON: config\expert_player_database_20250715_205514.json
2025-07-15 20:55:15,091 - expert_mapping_integration - INFO -    CSV: data\master\expert_player_database_20250715_205514.csv
2025-07-15 20:55:23,274 - supreme_autopilot_system - WARNING -    federated_server: Poor basketball context integration
2025-07-15 20:55:23,275 - supreme_autopilot_system - WARNING -    federated_team_atl: Poor basketball context integration
2025-07-15 20:55:23,275 - supreme_autopilot_system - WARNING -    federated_team_chi: Poor basketball context integration
2025-07-15 20:55:23,275 - supreme_autopilot_system - WARNING -    federated_team_con: Poor basketball context integration
2025-07-15 20:55:23,275 - supreme_autopilot_system - WARNING -    federated_team_dal: Poor basketball context integration
2025-07-15 20:55:23,276 - supreme_autopilot_system - WARNING -    federated_team_gsv: Poor basketball context integration
2025-07-15 20:55:23,276 - supreme_autopilot_system - WARNING -    federated_team_ind: Poor basketball context integration
2025-07-15 20:55:23,277 - supreme_autopilot_system - WARNING -    federated_team_las: Poor basketball context integration
2025-07-15 20:55:23,277 - supreme_autopilot_system - WARNING -    federated_team_lv: Poor basketball context integration
2025-07-15 20:55:23,278 - supreme_autopilot_system - WARNING -    federated_team_min: Poor basketball context integration
2025-07-15 20:55:23,278 - supreme_autopilot_system - WARNING -    federated_team_nyl: Poor basketball context integration
2025-07-15 20:55:23,278 - supreme_autopilot_system - WARNING -    federated_team_pho: Poor basketball context integration
2025-07-15 20:55:23,279 - supreme_autopilot_system - WARNING -    federated_team_sea: Poor basketball context integration
2025-07-15 20:55:23,279 - supreme_autopilot_system - WARNING -    federated_team_was: Poor basketball context integration
2025-07-15 20:55:23,279 - supreme_autopilot_system - WARNING -    possession_based_model: Poor basketball context integration
2025-07-15 20:55:23,279 - supreme_autopilot_system - WARNING -    lineup_chemistry_model: Poor basketball context integration
2025-07-15 20:55:23,280 - supreme_autopilot_system - WARNING -    arena_effect_model: Poor basketball context integration
2025-07-15 20:55:23,280 - supreme_autopilot_system - WARNING -    team_dynamics_model: Poor basketball context integration
2025-07-15 20:55:23,280 - supreme_autopilot_system - WARNING -    contextual_performance_model: Poor basketball context integration
2025-07-15 20:55:23,280 - supreme_autopilot_system - WARNING -    federated_multiverse_ensemble: Poor basketball context integration
2025-07-15 20:55:23,281 - supreme_autopilot_system - WARNING -    player_rebounds_model: Poor basketball context integration
2025-07-15 20:55:23,281 - supreme_autopilot_system - WARNING -    player_assists_model: Poor basketball context integration
2025-07-15 20:55:23,281 - supreme_autopilot_system - WARNING -    player_threes_model: Poor basketball context integration
2025-07-15 20:55:23,282 - supreme_autopilot_system - WARNING -    player_double_double_model: Poor basketball context integration
2025-07-15 20:55:23,282 - supreme_autopilot_system - WARNING -    pregame_win_probability_model: Poor basketball context integration
2025-07-15 20:55:23,282 - supreme_autopilot_system - WARNING -    live_win_probability_model: Poor basketball context integration
2025-07-15 20:55:23,282 - supreme_autopilot_system - WARNING -    enhanced_player_points_model: Poor basketball context integration
2025-07-15 20:55:23,283 - supreme_autopilot_system - WARNING -    hybrid_gnn_model: Poor basketball context integration
2025-07-15 20:55:23,283 - supreme_autopilot_system - WARNING -    multitask_model: Poor basketball context integration
2025-07-15 20:55:23,283 - supreme_autopilot_system - WARNING -    bayesian_model: Poor basketball context integration
2025-07-15 20:55:23,284 - supreme_autopilot_system - WARNING -    upset_prediction_model: Poor basketball context integration
2025-07-15 20:55:23,284 - supreme_autopilot_system - WARNING -    injury_impact_model: Poor basketball context integration
2025-07-15 20:55:23,285 - supreme_autopilot_system - WARNING -    momentum_analysis_model: Poor basketball context integration
2025-07-15 20:55:23,285 - supreme_autopilot_system - WARNING -    real_time_betting_intelligence: Poor basketball context integration
2025-07-15 20:55:23,285 - supreme_autopilot_system - WARNING -    win_probability_system: Poor basketball context integration
2025-07-15 20:55:23,285 - supreme_autopilot_system - WARNING -    live_espn_nba_api_integration: Poor basketball context integration
2025-07-15 20:55:23,286 - supreme_autopilot_system - WARNING -    live_nba_api_system: Poor basketball context integration
2025-07-15 20:55:23,286 - supreme_autopilot_system - WARNING -    live_espn_system: Poor basketball context integration
2025-07-15 20:55:23,286 - supreme_autopilot_system - WARNING -    hybrid_live_data_system: Poor basketball context integration
2025-07-15 20:55:23,286 - supreme_autopilot_system - WARNING -    elite_prediction_dashboard: Poor basketball context integration
2025-07-15 20:55:23,286 - supreme_autopilot_system - WARNING -    wnba_war_room_system: Poor basketball context integration
2025-07-15 20:55:23,287 - supreme_autopilot_system - WARNING -    monitoring_web_dashboard: Poor basketball context integration
2025-07-15 20:55:23,287 - supreme_autopilot_system - WARNING -    supreme_autopilot_system: Poor basketball context integration
2025-07-15 20:55:23,287 - supreme_autopilot_system - WARNING -    medusa_autopilot: Poor basketball context integration
2025-07-15 20:55:23,287 - supreme_autopilot_system - WARNING -    model_performance_autopilot: Poor basketball context integration
2025-07-15 20:55:23,287 - supreme_autopilot_system - WARNING -    system_health_monitor: Poor basketball context integration
2025-07-15 20:56:07,743 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/745aaf62cf453ecb85def61cd57639ec
2025-07-15 20:58:25,531 - supreme_autopilot_system - INFO - [AI] Initializing specialized AI delegation architecture...
2025-07-15 20:58:25,539 - central_cognitive_core - INFO - ?? All monitoring consolidated into Digital Immune System
2025-07-15 20:58:25,556 - digital_immune_system - WARNING - [WARN] No LLM API key found - Code healing will use fallback methods
2025-07-15 20:58:25,556 - digital_immune_system - INFO - [EXPERT] STARTING CONSOLIDATED MONITORING: All systems under Digital Immune System oversight
2025-07-15 20:58:25,558 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: system_health
2025-07-15 20:58:25,558 - digital_immune_system - INFO - [OK] system_health: Monitoring thread started
2025-07-15 20:58:25,582 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: performance_tracker
2025-07-15 20:58:25,582 - digital_immune_system - INFO - [OK] performance_tracker: Monitoring thread started
2025-07-15 20:58:25,583 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: resource_monitor
2025-07-15 20:58:25,583 - digital_immune_system - INFO - [OK] resource_monitor: Monitoring thread started
2025-07-15 20:58:25,583 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: api_health_checker
2025-07-15 20:58:25,583 - digital_immune_system - INFO - [OK] api_health_checker: Monitoring thread started
2025-07-15 20:58:25,584 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: model_performance_monitor
2025-07-15 20:58:25,584 - digital_immune_system - INFO - [OK] model_performance_monitor: Monitoring thread started
2025-07-15 20:58:25,584 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: federated_monitor
2025-07-15 20:58:25,584 - digital_immune_system - INFO - [OK] federated_monitor: Monitoring thread started
2025-07-15 20:58:25,585 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: basketball_intelligence_monitor
2025-07-15 20:58:25,585 - digital_immune_system - INFO - [OK] basketball_intelligence_monitor: Monitoring thread started
2025-07-15 20:58:25,585 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: betting_intelligence_monitor
2025-07-15 20:58:25,585 - digital_immune_system - INFO - [OK] betting_intelligence_monitor: Monitoring thread started
2025-07-15 20:58:25,585 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: dashboard_health_monitor
2025-07-15 20:58:25,586 - digital_immune_system - INFO - [OK] dashboard_health_monitor: Monitoring thread started
2025-07-15 20:58:25,586 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: live_system_monitor
2025-07-15 20:58:25,586 - digital_immune_system - INFO - [OK] live_system_monitor: Monitoring thread started
2025-07-15 20:58:25,586 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: model_endpoints_monitor
2025-07-15 20:58:25,587 - digital_immune_system - INFO - [OK] model_endpoints_monitor: Monitoring thread started
2025-07-15 20:58:25,587 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: wnba_architecture_evolution_monitor
2025-07-15 20:58:25,588 - digital_immune_system - INFO - [OK] wnba_architecture_evolution_monitor: Monitoring thread started
2025-07-15 20:58:25,590 - digital_immune_system - INFO - [DATA] CONSOLIDATED METRICS: Starting collection and analysis
2025-07-15 20:58:25,591 - digital_immune_system - INFO - [EXPERT] MONITORING ACTIVE: 13 monitoring threads running
2025-07-15 20:58:25,591 - digital_immune_system - INFO - [DATA] CONSOLIDATED STATUS: 10/12 monitors active, Health: 83.9%
2025-07-15 20:58:25,591 - digital_immune_system - INFO - ?? Digital Immune System initialized with predictive capabilities
2025-07-15 20:58:25,592 - digital_immune_system - INFO - [EXPERT] CONSOLIDATED MONITORING: All system monitoring delegated to Digital Immune System
2025-07-15 20:58:25,592 - causal_inference_engine - INFO - [AI] Causal Inference Engine initialized with pattern recognition
2025-07-15 20:58:25,594 - neuro_symbolic_policy_optimizer - INFO - ? Governance policy loaded successfully
2025-07-15 20:58:25,595 - neuro_symbolic_policy_optimizer - INFO - [AI] Neuro-Symbolic Policy Optimizer initialized
2025-07-15 20:58:25,608 - basketball_context_repair_system - INFO - [OK] Basketball context repair database initialized
2025-07-15 20:58:25,613 - basketball_context_repair_system - INFO - [WNBA] Basketball Context Repair System initialized
2025-07-15 20:58:25,614 - central_cognitive_core - INFO - [REPAIR] Running comprehensive basketball context repair for all models...
2025-07-15 20:58:25,614 - basketball_context_repair_system - INFO - [WNBA] STARTING COMPREHENSIVE BASKETBALL CONTEXT REPAIR
2025-07-15 20:58:25,614 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:58:25,615 - basketball_context_repair_system - ERROR - [ERROR] Communication layer not available
2025-07-15 20:58:25,615 - central_cognitive_core - INFO - [OK] Basketball context repair completed: 0 models processed
2025-07-15 20:58:25,616 - central_cognitive_core - INFO - ? Governance policy loaded successfully
2025-07-15 20:58:26,832 - military_grade_wnba_scraper - INFO - ?? Military-grade database initialized
2025-07-15 20:58:26,832 - military_grade_wnba_scraper - INFO - [EXPERT] Military-Grade WNBA Scraper initialized
2025-07-15 20:58:26,832 - military_grade_wnba_scraper - INFO - [TARGET] Targets: 13 configured
2025-07-15 20:58:26,832 - military_grade_wnba_scraper - INFO - ? Workers: 3 concurrent threads
2025-07-15 20:58:26,833 - military_grade_wnba_scraper - INFO - ?? Anti-detection: All systems armed
2025-07-15 20:58:26,833 - military_grade_wnba_scraper - INFO - [AI] Self-learning intelligence: ACTIVE
2025-07-15 20:58:26,833 - central_cognitive_core - INFO - [EXPERT] Military-grade WNBA scraper integrated into cognitive core
2025-07-15 20:58:26,833 - central_cognitive_core - INFO - [AI] COGNITIVE SCRAPING: Starting autonomous data collection
2025-07-15 20:58:26,833 - central_cognitive_core - INFO - [AI] Central Cognitive Core initialized with AGI architecture
2025-07-15 20:58:26,833 - central_cognitive_core - INFO - [EXPERT] COGNITIVE DECISION: Initiating military-grade scraping
2025-07-15 20:58:26,833 - supreme_autopilot_system - INFO - [AI] Central Cognitive Core DELEGATED as primary intelligence
2025-07-15 20:58:26,834 - military_grade_wnba_scraper - INFO - [EXPERT] MILITARY-GRADE SCRAPING INITIATED: Session 45195d6b84db
2025-07-15 20:58:26,834 - military_grade_wnba_scraper - INFO - [TARGET] Targets: 13 configured
2025-07-15 20:58:26,835 - military_grade_wnba_scraper - INFO - ? Workers: 3 concurrent threads
2025-07-15 20:58:26,841 - military_grade_wnba_scraper - INFO - [TARGET] PHASE 1: Military-grade props scraping
2025-07-15 20:58:26,842 - supreme_cache_management - INFO - ?? Cache database initialized
2025-07-15 20:58:26,842 - military_grade_wnba_scraper - INFO - [TARGET] MILITARY SCRAPING: DraftKings (Props) - Strategy: default
2025-07-15 20:58:26,847 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:26,847 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:26,848 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:26,848 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:26,849 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:26,850 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:26,850 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:26,850 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:26,850 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:26,850 - supreme_cache_management - INFO - [LAUNCH] All cache management threads started
2025-07-15 20:58:26,851 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:26,852 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:26,853 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:26,853 - supreme_cache_management - INFO - ?? Supreme Cache Management System initialized with live data
2025-07-15 20:58:26,857 - supreme_cache_management - INFO - ? Autopilot-controlled unified caching for all data sources
2025-07-15 20:58:26,858 - supreme_cache_management - INFO - [LIVE] Live play-by-play and player data integration ready
2025-07-15 20:58:26,858 - supreme_autopilot_system - INFO - ?? Supreme Cache Management with LIVE DATA ACTUALLY integrated
2025-07-15 20:58:26,858 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:26,859 - espn_api_connector - INFO - [WNBA] ESPN API Connector initialized for WNBA data
2025-07-15 20:58:26,859 - supreme_autopilot_system - INFO - [WNBA] Enhanced API Connectors with PLAY-BY-PLAY ACTUALLY integrated
2025-07-15 20:58:26,877 - supreme_cache_management - INFO - ? Updated injury status for 2 players
2025-07-15 20:58:27,033 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:27,034 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:27,034 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:27,043 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:27,044 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:27,049 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:27,092 - supreme_cache_management - INFO - [BET] Cached odds for 2 games
2025-07-15 20:58:27,198 - supreme_cache_management - INFO - ?? Cache database initialized
2025-07-15 20:58:27,199 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:27,200 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:27,200 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:27,201 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:27,202 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:27,203 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:27,203 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:27,205 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:27,205 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:27,207 - supreme_cache_management - INFO - [LAUNCH] All cache management threads started
2025-07-15 20:58:27,208 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:27,209 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:27,212 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:27,214 - supreme_cache_management - INFO - ?? Supreme Cache Management System initialized with live data
2025-07-15 20:58:27,218 - supreme_cache_management - INFO - ? Autopilot-controlled unified caching for all data sources
2025-07-15 20:58:27,219 - supreme_cache_management - INFO - [LIVE] Live play-by-play and player data integration ready
2025-07-15 20:58:27,220 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:27,220 - espn_api_connector - INFO - [WNBA] ESPN API Connector initialized for WNBA data
2025-07-15 20:58:27,232 - enhanced_unified_dashboard_server - INFO - [LAUNCH] Enhanced Unified Dashboard Server initialized
2025-07-15 20:58:27,233 - supreme_autopilot_system - INFO - [DATA] Enhanced Unified Dashboard Server with LIVE DATA ACTUALLY integrated
2025-07-15 20:58:27,239 - supreme_cache_management - INFO - ? Updated injury status for 2 players
2025-07-15 20:58:27,319 - expert_model_validation_system - INFO - ?? Model validation database initialized
2025-07-15 20:58:27,323 - expert_model_validation_system - INFO - [LAUNCH] All validation systems started
2025-07-15 20:58:27,325 - expert_model_validation_system - INFO - [TEST] Expert Model Validation System initialized
2025-07-15 20:58:27,326 - expert_model_validation_system - INFO - [TARGET] Production model control and dashboard feeds active
2025-07-15 20:58:27,327 - supreme_autopilot_system - INFO - [TEST] Expert Model Validation System ACTUALLY integrated
2025-07-15 20:58:27,331 - expert_model_validation_system - WARNING - ? Performance degradation detected: player_threes_model (0.000)
2025-07-15 20:58:27,332 - expert_model_validation_system - WARNING - ? Performance degradation detected: model_performance_autopilot (0.000)
2025-07-15 20:58:27,334 - expert_model_validation_system - WARNING - ? Performance degradation detected: team_dynamics_model (0.000)
2025-07-15 20:58:27,335 - expert_model_validation_system - WARNING - ? Performance degradation detected: possession_based_model (0.000)
2025-07-15 20:58:27,336 - expert_model_validation_system - WARNING - ? Performance degradation detected: elite_model_001 (0.000)
2025-07-15 20:58:27,337 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_multiverse_ensemble (0.000)
2025-07-15 20:58:27,338 - expert_model_validation_system - WARNING - ? Performance degradation detected: player_points_model (0.000)
2025-07-15 20:58:27,340 - expert_model_validation_system - WARNING - ? Performance degradation detected: player_rebounds_model (0.000)
2025-07-15 20:58:27,340 - expert_model_validation_system - WARNING - ? Performance degradation detected: player_assists_model (0.000)
2025-07-15 20:58:27,341 - expert_model_validation_system - WARNING - ? Performance degradation detected: hybrid_gnn_model (0.000)
2025-07-15 20:58:27,342 - expert_model_validation_system - WARNING - ? Performance degradation detected: multitask_model (0.000)
2025-07-15 20:58:27,343 - expert_model_validation_system - WARNING - ? Performance degradation detected: bayesian_model (0.000)
2025-07-15 20:58:27,343 - expert_model_validation_system - WARNING - ? Performance degradation detected: win_probability_system (0.000)
2025-07-15 20:58:27,343 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_atl (0.000)
2025-07-15 20:58:27,344 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_chi (0.000)
2025-07-15 20:58:27,344 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_con (0.000)
2025-07-15 20:58:27,344 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_dal (0.000)
2025-07-15 20:58:27,347 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_gsv (0.000)
2025-07-15 20:58:27,348 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:27,350 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:27,351 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_ind (0.000)
2025-07-15 20:58:27,353 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:27,354 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:27,354 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:27,355 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_las (0.000)
2025-07-15 20:58:27,357 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_lv (0.000)
2025-07-15 20:58:27,359 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_min (0.000)
2025-07-15 20:58:27,360 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_nyl (0.000)
2025-07-15 20:58:27,361 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_pho (0.000)
2025-07-15 20:58:27,362 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_sea (0.000)
2025-07-15 20:58:27,363 - expert_model_validation_system - WARNING - ? Performance degradation detected: federated_team_was (0.000)
2025-07-15 20:58:27,364 - expert_model_validation_system - WARNING - ? Performance degradation detected: team_model_001 (0.000)
2025-07-15 20:58:27,409 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:27,528 - supreme_cache_management - INFO - [BET] Cached odds for 2 games
2025-07-15 20:58:28,102 - military_grade_wnba_scraper - INFO - ?? Stealth driver created: 1616x939
2025-07-15 20:58:29,714 - expert_ml_modeling_system - INFO - [OK] Expert ML modeling database initialized
2025-07-15 20:58:29,715 - expert_ml_modeling_system - INFO - [AI] Expert ML Modeling System initialized
2025-07-15 20:58:29,716 - expert_ml_modeling_system - INFO - ?? Advanced feature engineering enabled
2025-07-15 20:58:29,716 - expert_ml_modeling_system - INFO - [TARGET] Expert validation strategies active
2025-07-15 20:58:29,717 - supreme_autopilot_system - INFO - [AI] Expert ML Modeling System ACTUALLY integrated
2025-07-15 20:58:29,717 - supreme_autopilot_system - INFO - ?? Advanced feature engineering enabled
2025-07-15 20:58:29,718 - supreme_autopilot_system - INFO - [TARGET] Expert validation strategies active
2025-07-15 20:58:30,636 - expert_wnba_betting_system - INFO - [OK] Elite betting database initialized
2025-07-15 20:58:30,637 - expert_wnba_betting_system - INFO - [OK] Loaded player performance models
2025-07-15 20:58:30,637 - expert_wnba_betting_system - INFO - [OK] Loaded team performance models
2025-07-15 20:58:30,638 - expert_wnba_betting_system - INFO - [TARGET] ELITE WNBA Betting System initialized
2025-07-15 20:58:30,639 - expert_wnba_betting_system - INFO - [BET] Bankroll: $10000.00
2025-07-15 20:58:30,640 - expert_wnba_betting_system - INFO - [AI] Advanced RAPTOR metrics and market intelligence integrated
2025-07-15 20:58:30,640 - expert_wnba_betting_system - INFO - [EXEC] Live betting capabilities enabled
2025-07-15 20:58:30,641 - supreme_autopilot_system - INFO - [TARGET] Expert WNBA Betting System ACTUALLY integrated
2025-07-15 20:58:30,641 - supreme_autopilot_system - INFO - [BET] Advanced market analysis and +EV identification enabled
2025-07-15 20:58:30,643 - supreme_autopilot_system - INFO - [AI] Tactical and situational factors integrated
2025-07-15 20:58:30,643 - supreme_autopilot_system - INFO - [DATA] Pace-adjusted stats and efficiency metrics active
2025-07-15 20:58:30,644 - supreme_autopilot_system - INFO - [TARGET] Expert Betting Intelligence System initialized
2025-07-15 20:58:30,644 - supreme_autopilot_system - INFO - [TARGET] Expert Real-Time Betting Intelligence integrated into Supreme Autopilot
2025-07-15 20:58:30,645 - supreme_autopilot_system - INFO - [AI] Expert prop analysis and recommendation system active
2025-07-15 20:58:30,645 - supreme_autopilot_system - INFO - [DATA] Live betting intelligence operational
2025-07-15 20:58:30,646 - supreme_autopilot_system - INFO - [SEARCH] Expert market analysis enabled
2025-07-15 20:58:30,653 - win_probability_system - INFO - [OK] Win probability database initialized
2025-07-15 20:58:30,653 - win_probability_system - INFO - [AI] Initializing ML models for win probability calculations...
2025-07-15 20:58:30,679 - autopilot_model_communication_layer - INFO - ?? Communication database initialized
2025-07-15 20:58:30,685 - autopilot_model_communication_layer - INFO - [LAUNCH] All communication threads started
2025-07-15 20:58:30,686 - autopilot_model_communication_layer - INFO - ? Autopilot-Model Communication Layer initialized
2025-07-15 20:58:30,686 - win_probability_system - INFO - [OK] Connected to autopilot model communication layer
2025-07-15 20:58:30,694 - supreme_cache_management - INFO - ?? Cache database initialized
2025-07-15 20:58:30,700 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:30,700 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:30,702 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:30,705 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:30,705 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:30,706 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:30,707 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:30,708 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:30,710 - supreme_cache_management - INFO - [LAUNCH] All cache management threads started
2025-07-15 20:58:30,710 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:30,708 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:30,711 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:30,712 - supreme_cache_management - INFO - ?? Supreme Cache Management System initialized with live data
2025-07-15 20:58:30,715 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:30,718 - supreme_cache_management - INFO - ? Autopilot-controlled unified caching for all data sources
2025-07-15 20:58:30,721 - supreme_cache_management - INFO - [LIVE] Live play-by-play and player data integration ready
2025-07-15 20:58:30,724 - win_probability_system - INFO - [OK] Connected to historical data engine (10 years of WNBA data)
2025-07-15 20:58:30,725 - win_probability_system - INFO - [OK] ML Models initialized:
2025-07-15 20:58:30,725 - win_probability_system - INFO -    [NET] Federated models: 13 teams
2025-07-15 20:58:30,726 - win_probability_system - INFO -    ? Multiverse ensemble: 6 models
2025-07-15 20:58:30,727 - win_probability_system - INFO -    [AI] Neural networks: 4 models
2025-07-15 20:58:30,728 - win_probability_system - INFO -    [DATA] Historical data: Available
2025-07-15 20:58:30,729 - supreme_cache_management - INFO - ? Updated injury status for 2 players
2025-07-15 20:58:30,733 - win_probability_system - INFO - [LAUNCH] Win probability update threads started
2025-07-15 20:58:30,736 - win_probability_system - INFO - ? Win Probability System initialized
2025-07-15 20:58:30,737 - win_probability_system - INFO - [AI] ML Models integrated: Federated + Multiverse + Neural Networks
2025-07-15 20:58:30,738 - win_probability_system - INFO - [DATA] Real-time game probability tracking enabled
2025-07-15 20:58:30,740 - real_wnba_data_fetcher - INFO - [WNBA] Real WNBA Data Fetcher initialized
2025-07-15 20:58:30,740 - real_wnba_data_fetcher - INFO - [WNBA] Real WNBA Data Fetcher initialized
2025-07-15 20:58:30,741 - win_probability_system - INFO - [TARGET] Dashboard integration ready
2025-07-15 20:58:30,741 - real_wnba_data_fetcher - INFO - [DATE] Fetching today's real WNBA games...
2025-07-15 20:58:30,742 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 20:58:30,744 - supreme_autopilot_system - INFO - ? Win Probability System ACTUALLY integrated
2025-07-15 20:58:30,747 - supreme_autopilot_system - INFO - [DATA] Real-time game probability tracking enabled
2025-07-15 20:58:30,748 - supreme_autopilot_system - INFO - [TARGET] Dashboard win probability integration ready
2025-07-15 20:58:30,750 - supreme_autopilot_system - INFO - [EXEC] Live game probability updates operational
2025-07-15 20:58:30,756 - supreme_autopilot_model_registry - INFO - ? Supreme Autopilot Model Registry initialized with 36 models
2025-07-15 20:58:30,756 - supreme_autopilot_system - INFO - ? Supreme Autopilot Model Registry ACTUALLY integrated
2025-07-15 20:58:30,757 - supreme_autopilot_system - INFO - [DATA] Managing 36 models across 7 categories
2025-07-15 20:58:30,757 - supreme_autopilot_system - INFO -    [TARGET] Federated Learning: 14 models
2025-07-15 20:58:30,757 - supreme_autopilot_system - INFO -    [TARGET] Multiverse Ensemble: 6 models
2025-07-15 20:58:30,759 - supreme_autopilot_system - INFO -    [TARGET] Expert Alternate Stats: 4 models
2025-07-15 20:58:30,759 - supreme_autopilot_system - INFO -    [TARGET] Win Probability: 2 models
2025-07-15 20:58:30,759 - supreme_autopilot_system - INFO -    [TARGET] Enhanced Player Models: 4 models
2025-07-15 20:58:30,760 - supreme_autopilot_system - INFO -    [TARGET] Specialized Prediction: 3 models
2025-07-15 20:58:30,760 - supreme_autopilot_system - INFO -    [TARGET] Real-Time Systems: 3 models
2025-07-15 20:58:30,760 - supreme_autopilot_system - INFO - [TARGET] Unified Dashboard Server control initialized
2025-07-15 20:58:30,778 - autopilot_model_communication_layer - INFO - ?? WebSocket server skipped: Using HTTP communication instead
2025-07-15 20:58:30,826 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:30,826 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:30,828 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:30,834 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:30,856 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:30,868 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:30,944 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 20:58:30,944 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 20:58:30,945 - win_probability_system - INFO - [TARGET] Calculating pregame win probability: IND @ CONN
2025-07-15 20:58:30,945 - win_probability_system - INFO - [AI] Using ML models: Federated + Multiverse + Neural Networks
2025-07-15 20:58:30,954 - supreme_cache_management - INFO - [BET] Cached odds for 2 games
2025-07-15 20:58:31,019 - real_wnba_data_fetcher - INFO - [OK] ESPN: Found 2 games today
2025-07-15 20:58:31,020 - win_probability_system - INFO - [OK] Found 2 real WNBA games today
2025-07-15 20:58:31,020 - win_probability_system - INFO - [TARGET] Calculating pregame win probability: WSH @ LA
2025-07-15 20:58:31,021 - win_probability_system - INFO - [AI] Using ML models: Federated + Multiverse + Neural Networks
2025-07-15 20:58:31,314 - win_probability_system - INFO - [OK] LA win probability: 56.5% (confidence: 92.3%)
2025-07-15 20:58:31,319 - win_probability_system - INFO - [OK] CONN win probability: 56.5% (confidence: 92.4%)
2025-07-15 20:58:34,911 - simple_federated_multiverse_integration - INFO - [OK] Federated multiverse database initialized
2025-07-15 20:58:34,935 - simple_federated_multiverse_integration - INFO - [OK] Initialized 13 federated teams
2025-07-15 20:58:34,948 - simple_federated_multiverse_integration - INFO - [OK] Initialized 5 multiverse models
2025-07-15 20:58:34,948 - simple_federated_multiverse_integration - INFO - [NET] Federated Multiverse Integration initialized
2025-07-15 20:58:34,948 - simple_federated_multiverse_integration - INFO - ? 13 WNBA team federated learning ready
2025-07-15 20:58:34,948 - simple_federated_multiverse_integration - INFO - ? 5 specialized multiverse ensemble models ready
2025-07-15 20:58:34,948 - simple_federated_multiverse_integration - INFO - ?? 60% Federated + 40% Multiverse weighting configured
2025-07-15 20:58:34,948 - supreme_autopilot_system - INFO - [NET] Simple Federated Multiverse Integration integrated (fallback)
2025-07-15 20:58:34,949 - supreme_autopilot_system - INFO - ? 13 WNBA team federated learning active
2025-07-15 20:58:34,949 - supreme_autopilot_system - INFO - ? 5 specialized multiverse ensemble models operational
2025-07-15 20:58:34,949 - supreme_autopilot_system - INFO - ?? 60% Federated + 40% Multiverse weighting applied
2025-07-15 20:58:34,954 - autopilot_player_props_system - INFO - [OK] Props database initialized
2025-07-15 20:58:34,954 - autopilot_player_props_system - INFO - [BET] Autopilot Player Props System initialized
2025-07-15 20:58:34,954 - supreme_autopilot_system - INFO - [TARGET] Autopilot Player Props System ACTUALLY integrated
2025-07-15 20:58:34,955 - supreme_autopilot_system - INFO - [AI] Scraping operations delegated to Central Cognitive Core
2025-07-15 20:58:34,961 - self_learning_error_resolver - INFO - ?? Error resolver database initialized
2025-07-15 20:58:34,966 - self_learning_error_resolver - INFO - [LAUNCH] Error monitoring systems started
2025-07-15 20:58:34,967 - self_learning_error_resolver - INFO - [AI] Self-Learning Error Resolver initialized
2025-07-15 20:58:34,967 - self_learning_error_resolver - INFO - [SEARCH] Intelligent error detection and resolution active
2025-07-15 20:58:34,967 - supreme_autopilot_system - INFO - [AI] Self-Learning Error Resolver ACTUALLY integrated
2025-07-15 20:58:34,972 - autopilot_model_communication_layer - INFO - ?? Communication database initialized
2025-07-15 20:58:34,978 - autopilot_model_communication_layer - INFO - [LAUNCH] All communication threads started
2025-07-15 20:58:34,978 - autopilot_model_communication_layer - INFO - ? Autopilot-Model Communication Layer initialized
2025-07-15 20:58:34,978 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_server
2025-07-15 20:58:34,978 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_atl
2025-07-15 20:58:34,979 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_chi
2025-07-15 20:58:34,979 - autopilot_model_communication_layer - INFO - ?? WebSocket server skipped: Using HTTP communication instead
2025-07-15 20:58:34,979 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_con
2025-07-15 20:58:34,979 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_dal
2025-07-15 20:58:34,979 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_gsv
2025-07-15 20:58:34,980 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_ind
2025-07-15 20:58:34,980 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_las
2025-07-15 20:58:34,980 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_lv
2025-07-15 20:58:34,980 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_min
2025-07-15 20:58:34,981 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_nyl
2025-07-15 20:58:34,981 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_pho
2025-07-15 20:58:34,982 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_sea
2025-07-15 20:58:34,984 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_team_was
2025-07-15 20:58:34,985 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: possession_based_model
2025-07-15 20:58:34,985 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: lineup_chemistry_model
2025-07-15 20:58:34,986 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: arena_effect_model
2025-07-15 20:58:34,986 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: team_dynamics_model
2025-07-15 20:58:34,986 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: contextual_performance_model
2025-07-15 20:58:34,987 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: federated_multiverse_ensemble
2025-07-15 20:58:34,987 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: player_rebounds_model
2025-07-15 20:58:34,987 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: player_assists_model
2025-07-15 20:58:34,988 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: player_threes_model
2025-07-15 20:58:34,988 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: player_double_double_model
2025-07-15 20:58:34,988 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: pregame_win_probability_model
2025-07-15 20:58:34,989 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: live_win_probability_model
2025-07-15 20:58:34,989 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: enhanced_player_points_model
2025-07-15 20:58:34,989 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: hybrid_gnn_model
2025-07-15 20:58:34,989 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: multitask_model
2025-07-15 20:58:34,990 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: bayesian_model
2025-07-15 20:58:34,990 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: upset_prediction_model
2025-07-15 20:58:34,990 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: injury_impact_model
2025-07-15 20:58:34,990 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: momentum_analysis_model
2025-07-15 20:58:34,990 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: real_time_betting_intelligence
2025-07-15 20:58:34,991 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: win_probability_system
2025-07-15 20:58:34,991 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: live_espn_nba_api_integration
2025-07-15 20:58:34,991 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: live_nba_api_system
2025-07-15 20:58:34,991 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: live_espn_system
2025-07-15 20:58:34,991 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: hybrid_live_data_system
2025-07-15 20:58:34,991 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: elite_prediction_dashboard
2025-07-15 20:58:34,991 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: wnba_war_room_system
2025-07-15 20:58:34,991 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: monitoring_web_dashboard
2025-07-15 20:58:34,994 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: supreme_autopilot_system
2025-07-15 20:58:34,995 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: medusa_autopilot
2025-07-15 20:58:34,995 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: model_performance_autopilot
2025-07-15 20:58:34,996 - autopilot_model_communication_layer - INFO - [NOTE] Model registered: system_health_monitor
2025-07-15 20:58:34,997 - supreme_autopilot_system - INFO - [NOTE] Registered ALL 46 models with communication layer
2025-07-15 20:58:34,997 - supreme_autopilot_system - INFO - [TARGET] Complete model coverage: 46 total models under Supreme Autopilot governance
2025-07-15 20:58:34,997 - supreme_autopilot_system - INFO - ? Autopilot-Model Communication Layer integrated
2025-07-15 20:58:35,029 - self_directed_architecture_evolution - INFO - ? Self-Directed Architecture Evolution System initialized
2025-07-15 20:58:35,029 - supreme_autopilot_system - INFO - ? EXPERT Self-Directed Architecture Evolution integrated with REAL health monitoring
2025-07-15 20:58:35,029 - central_cognitive_core - INFO - ?? All monitoring consolidated into Digital Immune System
2025-07-15 20:58:35,030 - digital_immune_system - WARNING - [WARN] No LLM API key found - Code healing will use fallback methods
2025-07-15 20:58:35,030 - digital_immune_system - INFO - [EXPERT] STARTING CONSOLIDATED MONITORING: All systems under Digital Immune System oversight
2025-07-15 20:58:35,032 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: system_health
2025-07-15 20:58:35,032 - digital_immune_system - INFO - [OK] system_health: Monitoring thread started
2025-07-15 20:58:35,055 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: performance_tracker
2025-07-15 20:58:35,055 - digital_immune_system - INFO - [OK] performance_tracker: Monitoring thread started
2025-07-15 20:58:35,057 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: resource_monitor
2025-07-15 20:58:35,057 - digital_immune_system - INFO - [OK] resource_monitor: Monitoring thread started
2025-07-15 20:58:35,061 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: api_health_checker
2025-07-15 20:58:35,061 - digital_immune_system - INFO - [OK] api_health_checker: Monitoring thread started
2025-07-15 20:58:35,064 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: model_performance_monitor
2025-07-15 20:58:35,065 - digital_immune_system - INFO - [OK] model_performance_monitor: Monitoring thread started
2025-07-15 20:58:35,067 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: federated_monitor
2025-07-15 20:58:35,067 - digital_immune_system - INFO - [OK] federated_monitor: Monitoring thread started
2025-07-15 20:58:35,068 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: basketball_intelligence_monitor
2025-07-15 20:58:35,068 - digital_immune_system - INFO - [OK] basketball_intelligence_monitor: Monitoring thread started
2025-07-15 20:58:35,069 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: betting_intelligence_monitor
2025-07-15 20:58:35,069 - digital_immune_system - INFO - [OK] betting_intelligence_monitor: Monitoring thread started
2025-07-15 20:58:35,070 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: dashboard_health_monitor
2025-07-15 20:58:35,070 - digital_immune_system - INFO - [OK] dashboard_health_monitor: Monitoring thread started
2025-07-15 20:58:35,071 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: live_system_monitor
2025-07-15 20:58:35,072 - digital_immune_system - INFO - [OK] live_system_monitor: Monitoring thread started
2025-07-15 20:58:35,073 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: model_endpoints_monitor
2025-07-15 20:58:35,073 - digital_immune_system - INFO - [OK] model_endpoints_monitor: Monitoring thread started
2025-07-15 20:58:35,074 - digital_immune_system - INFO - [SEARCH] MONITOR ACTIVE: wnba_architecture_evolution_monitor
2025-07-15 20:58:35,074 - digital_immune_system - INFO - [OK] wnba_architecture_evolution_monitor: Monitoring thread started
2025-07-15 20:58:35,075 - digital_immune_system - INFO - [DATA] CONSOLIDATED METRICS: Starting collection and analysis
2025-07-15 20:58:35,075 - digital_immune_system - INFO - [EXPERT] MONITORING ACTIVE: 13 monitoring threads running
2025-07-15 20:58:35,075 - digital_immune_system - INFO - [DATA] CONSOLIDATED STATUS: 10/12 monitors active, Health: 83.5%
2025-07-15 20:58:35,076 - digital_immune_system - INFO - ?? Digital Immune System initialized with predictive capabilities
2025-07-15 20:58:35,076 - digital_immune_system - INFO - [EXPERT] CONSOLIDATED MONITORING: All system monitoring delegated to Digital Immune System
2025-07-15 20:58:35,076 - causal_inference_engine - INFO - [AI] Causal Inference Engine initialized with pattern recognition
2025-07-15 20:58:35,076 - neuro_symbolic_policy_optimizer - INFO - ? Governance policy loaded successfully
2025-07-15 20:58:35,076 - neuro_symbolic_policy_optimizer - INFO - [AI] Neuro-Symbolic Policy Optimizer initialized
2025-07-15 20:58:35,078 - basketball_context_repair_system - INFO - [OK] Basketball context repair database initialized
2025-07-15 20:58:35,079 - basketball_context_repair_system - INFO - [WNBA] Basketball Context Repair System initialized
2025-07-15 20:58:35,080 - central_cognitive_core - INFO - [REPAIR] Running comprehensive basketball context repair for all models...
2025-07-15 20:58:35,080 - basketball_context_repair_system - INFO - [WNBA] STARTING COMPREHENSIVE BASKETBALL CONTEXT REPAIR
2025-07-15 20:58:35,080 - basketball_context_repair_system - INFO - ============================================================
2025-07-15 20:58:35,082 - basketball_context_repair_system - INFO - [WNBA] Generated rich basketball context with 7 components
2025-07-15 20:58:37,101 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:37,103 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:37,412 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:37,412 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:37,500 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:37,535 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:39,105 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_server
2025-07-15 20:58:39,114 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_server: basketball_context_repair (Priority: 1)
2025-07-15 20:58:39,123 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_server: integrate_basketball_context (Priority: 1)
2025-07-15 20:58:39,193 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_server: basketball_context_repair
2025-07-15 20:58:39,194 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_server
2025-07-15 20:58:39,296 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_server: integrate_basketball_context
2025-07-15 20:58:39,297 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_server
2025-07-15 20:58:40,866 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:40,866 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:41,144 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:41,329 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 20:58:41,660 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 20:58:41,661 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 20:58:46,150 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_server: 1.000 (PRODUCTION READY)
2025-07-15 20:58:46,151 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_server (score: 1.000)
2025-07-15 20:58:46,163 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_server: trigger_expert_validation (Priority: 1)
2025-07-15 20:58:46,164 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_server: cmd_1752627526152
2025-07-15 20:58:46,173 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_server (score: 1.000)
2025-07-15 20:58:46,183 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_server: trigger_expert_validation (Priority: 1)
2025-07-15 20:58:46,184 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_server: cmd_1752627526173
2025-07-15 20:58:46,184 - basketball_context_repair_system - INFO - [OK] federated_server: 0.000 ? 1.000
2025-07-15 20:58:46,229 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_server: trigger_expert_validation
2025-07-15 20:58:46,230 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_server
2025-07-15 20:58:46,331 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_server: trigger_expert_validation
2025-07-15 20:58:46,332 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_server
2025-07-15 20:58:47,505 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:47,508 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:47,545 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:47,545 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:47,675 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:47,680 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:50,199 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_atl
2025-07-15 20:58:50,214 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_atl: basketball_context_repair (Priority: 1)
2025-07-15 20:58:50,221 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_atl: integrate_basketball_context (Priority: 1)
2025-07-15 20:58:50,249 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_atl: basketball_context_repair
2025-07-15 20:58:50,249 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_atl
2025-07-15 20:58:50,340 - digital_immune_system - WARNING - ? model_endpoints_monitor: 1 threats detected
2025-07-15 20:58:50,350 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_atl: integrate_basketball_context
2025-07-15 20:58:50,351 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_atl
2025-07-15 20:58:51,165 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:51,166 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:51,332 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:51,674 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 20:58:51,863 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 20:58:51,864 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 20:58:57,039 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:57,040 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:57,075 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:57,076 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:57,172 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:57,229 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:57,249 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_atl: 1.000 (PRODUCTION READY)
2025-07-15 20:58:57,250 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_atl (score: 1.000)
2025-07-15 20:58:57,262 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_atl: trigger_expert_validation (Priority: 1)
2025-07-15 20:58:57,263 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_atl: cmd_1752627537251
2025-07-15 20:58:57,276 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_atl (score: 1.000)
2025-07-15 20:58:57,284 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_atl: trigger_expert_validation
2025-07-15 20:58:57,285 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_atl
2025-07-15 20:58:57,295 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_atl: trigger_expert_validation (Priority: 1)
2025-07-15 20:58:57,297 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_atl: cmd_1752627537277
2025-07-15 20:58:57,298 - basketball_context_repair_system - INFO - [OK] federated_team_atl: 0.000 ? 1.000
2025-07-15 20:58:57,380 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:57,381 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:57,387 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_atl: trigger_expert_validation
2025-07-15 20:58:57,388 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_atl
2025-07-15 20:58:57,426 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:57,426 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:57,528 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:57,573 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:57,686 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:57,689 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:57,712 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:58:57,713 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:58:57,830 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:57,842 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:58:59,716 - digital_immune_system - WARNING - ? model_endpoints_monitor: 1 threats detected
2025-07-15 20:59:00,880 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:00,881 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:00,909 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:00,910 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:01,012 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:01,058 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:01,315 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_chi
2025-07-15 20:59:01,324 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_chi: basketball_context_repair (Priority: 1)
2025-07-15 20:59:01,332 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_chi: integrate_basketball_context (Priority: 1)
2025-07-15 20:59:01,351 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:01,352 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:01,410 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_chi: basketball_context_repair
2025-07-15 20:59:01,410 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_chi
2025-07-15 20:59:01,491 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:01,511 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_chi: integrate_basketball_context
2025-07-15 20:59:01,511 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_chi
2025-07-15 20:59:01,874 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 20:59:02,095 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 20:59:02,096 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 20:59:06,731 - digital_immune_system - WARNING - ? dashboard_health_monitor: 1 threats detected
2025-07-15 20:59:07,845 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:07,846 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:07,862 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:07,863 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:08,042 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:08,043 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:08,355 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_chi: 1.000 (PRODUCTION READY)
2025-07-15 20:59:08,355 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_chi (score: 1.000)
2025-07-15 20:59:08,367 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_chi: trigger_expert_validation (Priority: 1)
2025-07-15 20:59:08,368 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_chi: cmd_1752627548356
2025-07-15 20:59:08,376 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_chi (score: 1.000)
2025-07-15 20:59:08,387 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_chi: trigger_expert_validation (Priority: 1)
2025-07-15 20:59:08,387 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_chi: cmd_1752627548377
2025-07-15 20:59:08,388 - basketball_context_repair_system - INFO - [OK] federated_team_chi: 0.000 ? 1.000
2025-07-15 20:59:08,440 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_chi: trigger_expert_validation
2025-07-15 20:59:08,441 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_chi
2025-07-15 20:59:08,542 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_chi: trigger_expert_validation
2025-07-15 20:59:08,543 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_chi
2025-07-15 20:59:11,497 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:11,497 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:11,648 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:12,101 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 20:59:12,291 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 20:59:12,292 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 20:59:12,414 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_con
2025-07-15 20:59:12,427 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_con: basketball_context_repair (Priority: 1)
2025-07-15 20:59:12,440 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_con: integrate_basketball_context (Priority: 1)
2025-07-15 20:59:12,461 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_con: basketball_context_repair
2025-07-15 20:59:12,463 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_con
2025-07-15 20:59:12,565 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_con: integrate_basketball_context
2025-07-15 20:59:12,565 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_con
2025-07-15 20:59:16,093 - digital_immune_system - WARNING - ? dashboard_health_monitor: 1 threats detected
2025-07-15 20:59:18,051 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:18,051 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:18,057 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:18,058 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:18,193 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:18,194 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:19,456 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_con: 1.000 (PRODUCTION READY)
2025-07-15 20:59:19,457 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_con (score: 1.000)
2025-07-15 20:59:19,462 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_con: trigger_expert_validation (Priority: 1)
2025-07-15 20:59:19,463 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_con: cmd_1752627559457
2025-07-15 20:59:19,470 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_con (score: 1.000)
2025-07-15 20:59:19,487 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_con: trigger_expert_validation (Priority: 1)
2025-07-15 20:59:19,487 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_con: cmd_1752627559470
2025-07-15 20:59:19,489 - basketball_context_repair_system - INFO - [OK] federated_team_con: 0.000 ? 1.000
2025-07-15 20:59:19,496 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_con: trigger_expert_validation
2025-07-15 20:59:19,497 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_con
2025-07-15 20:59:19,597 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_con: trigger_expert_validation
2025-07-15 20:59:19,598 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_con
2025-07-15 20:59:21,660 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:21,661 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:21,832 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:22,299 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 20:59:22,484 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 20:59:22,485 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 20:59:23,517 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_dal
2025-07-15 20:59:23,529 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_dal: basketball_context_repair (Priority: 1)
2025-07-15 20:59:23,536 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_dal: integrate_basketball_context (Priority: 1)
2025-07-15 20:59:23,612 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_dal: basketball_context_repair
2025-07-15 20:59:23,613 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_dal
2025-07-15 20:59:23,713 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_dal: integrate_basketball_context
2025-07-15 20:59:23,714 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_dal
2025-07-15 20:59:27,075 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:27,076 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:27,190 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:27,191 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:27,213 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:27,239 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:27,240 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:27,326 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:27,394 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:27,394 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:27,400 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:27,521 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:27,543 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:27,543 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:27,589 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:27,589 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:27,712 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:27,719 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:28,199 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:28,200 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:28,217 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:28,217 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:28,360 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:28,372 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:30,562 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_dal: 1.000 (PRODUCTION READY)
2025-07-15 20:59:30,562 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_dal (score: 1.000)
2025-07-15 20:59:30,576 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_dal: trigger_expert_validation (Priority: 1)
2025-07-15 20:59:30,577 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_dal: cmd_1752627570563
2025-07-15 20:59:30,588 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_dal (score: 1.000)
2025-07-15 20:59:30,597 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_dal: trigger_expert_validation (Priority: 1)
2025-07-15 20:59:30,598 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_dal: cmd_1752627570589
2025-07-15 20:59:30,599 - basketball_context_repair_system - INFO - [OK] federated_team_dal: 0.000 ? 1.000
2025-07-15 20:59:30,645 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_dal: trigger_expert_validation
2025-07-15 20:59:30,647 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_dal
2025-07-15 20:59:30,749 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_dal: trigger_expert_validation
2025-07-15 20:59:30,751 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_dal
2025-07-15 20:59:30,848 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:30,849 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:30,983 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:31,025 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:31,026 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:31,071 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:31,072 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:31,158 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:31,202 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:31,850 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:31,851 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:32,031 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:32,490 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 20:59:32,684 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 20:59:32,685 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 20:59:34,618 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_gsv
2025-07-15 20:59:34,631 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_gsv: basketball_context_repair (Priority: 1)
2025-07-15 20:59:34,642 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_gsv: integrate_basketball_context (Priority: 1)
2025-07-15 20:59:34,668 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_gsv: basketball_context_repair
2025-07-15 20:59:34,669 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_gsv
2025-07-15 20:59:34,772 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_gsv: integrate_basketball_context
2025-07-15 20:59:34,774 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_gsv
2025-07-15 20:59:38,371 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:38,371 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:38,383 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:38,383 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:38,502 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:38,507 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:41,667 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_gsv: 1.000 (PRODUCTION READY)
2025-07-15 20:59:41,668 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_gsv (score: 1.000)
2025-07-15 20:59:41,675 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_gsv: trigger_expert_validation (Priority: 1)
2025-07-15 20:59:41,677 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_gsv: cmd_1752627581668
2025-07-15 20:59:41,686 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_gsv (score: 1.000)
2025-07-15 20:59:41,697 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_gsv: trigger_expert_validation (Priority: 1)
2025-07-15 20:59:41,698 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_gsv: cmd_1752627581687
2025-07-15 20:59:41,699 - basketball_context_repair_system - INFO - [OK] federated_team_gsv: 0.000 ? 1.000
2025-07-15 20:59:41,704 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_gsv: trigger_expert_validation
2025-07-15 20:59:41,705 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_gsv
2025-07-15 20:59:41,807 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_gsv: trigger_expert_validation
2025-07-15 20:59:41,807 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_gsv
2025-07-15 20:59:42,037 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:42,038 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:42,151 - military_grade_wnba_scraper - INFO - ?? APPLYING SOLUTION: generic_retry for extraction error
2025-07-15 20:59:42,152 - military_grade_wnba_scraper - INFO - ?? FALLBACK: Using basic extraction for DraftKings
2025-07-15 20:59:42,251 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:42,692 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 20:59:42,891 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 20:59:42,892 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 20:59:45,491 - military_grade_wnba_scraper - INFO - ?? FALLBACK: Extracted 2 props using basic selectors
2025-07-15 20:59:45,498 - military_grade_wnba_scraper - INFO - [SAVE] Saved 2 props to database
2025-07-15 20:59:45,734 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_ind
2025-07-15 20:59:45,743 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_ind: basketball_context_repair (Priority: 1)
2025-07-15 20:59:45,749 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_ind: integrate_basketball_context (Priority: 1)
2025-07-15 20:59:45,826 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_ind: basketball_context_repair
2025-07-15 20:59:45,827 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_ind
2025-07-15 20:59:45,928 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_ind: integrate_basketball_context
2025-07-15 20:59:45,928 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_ind
2025-07-15 20:59:47,701 - military_grade_wnba_scraper - INFO - [OK] DraftKings: 2 props collected in 80.86s (Quality: 1.00)
2025-07-15 20:59:47,705 - military_grade_wnba_scraper - INFO - [TARGET] MILITARY SCRAPING: FanDuel (Props) - Strategy: default
2025-07-15 20:59:48,511 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:48,512 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:48,557 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:48,558 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:48,826 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:48,828 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:49,206 - military_grade_wnba_scraper - INFO - ?? Stealth driver created: 1535x805
2025-07-15 20:59:52,259 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:52,259 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:52,410 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:52,803 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_ind: 1.000 (PRODUCTION READY)
2025-07-15 20:59:52,803 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_ind (score: 1.000)
2025-07-15 20:59:52,811 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_ind: trigger_expert_validation (Priority: 1)
2025-07-15 20:59:52,811 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_ind: cmd_1752627592803
2025-07-15 20:59:52,821 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_ind (score: 1.000)
2025-07-15 20:59:52,833 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_ind: trigger_expert_validation (Priority: 1)
2025-07-15 20:59:52,833 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_ind: cmd_1752627592822
2025-07-15 20:59:52,835 - basketball_context_repair_system - INFO - [OK] federated_team_ind: 0.000 ? 1.000
2025-07-15 20:59:52,864 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_ind: trigger_expert_validation
2025-07-15 20:59:52,865 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_ind
2025-07-15 20:59:52,895 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 20:59:52,967 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_ind: trigger_expert_validation
2025-07-15 20:59:52,968 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_ind
2025-07-15 20:59:53,130 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 20:59:53,131 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 20:59:56,859 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_las
2025-07-15 20:59:56,879 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_las: basketball_context_repair (Priority: 1)
2025-07-15 20:59:56,888 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_las: basketball_context_repair
2025-07-15 20:59:56,890 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_las
2025-07-15 20:59:56,892 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_las: integrate_basketball_context (Priority: 1)
2025-07-15 20:59:56,991 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_las: integrate_basketball_context
2025-07-15 20:59:56,992 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_las
2025-07-15 20:59:57,334 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:57,335 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:57,408 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:57,410 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:57,555 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:57,593 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:57,717 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:57,718 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:57,730 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:57,731 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:57,862 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:57,885 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:58,834 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:58,834 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:58,839 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 20:59:58,839 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 20:59:58,989 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 20:59:59,017 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:01,173 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:01,173 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:01,208 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:01,208 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:01,409 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:01,413 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:02,421 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:02,422 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:02,923 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:03,137 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 21:00:03,559 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 21:00:03,560 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 21:00:03,925 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_las: 1.000 (PRODUCTION READY)
2025-07-15 21:00:03,926 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_las (score: 1.000)
2025-07-15 21:00:03,926 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_las: trigger_expert_validation
2025-07-15 21:00:03,927 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_las
2025-07-15 21:00:03,935 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_las: trigger_expert_validation (Priority: 1)
2025-07-15 21:00:03,936 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_las: cmd_1752627603926
2025-07-15 21:00:03,942 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_las (score: 1.000)
2025-07-15 21:00:03,948 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_las: trigger_expert_validation (Priority: 1)
2025-07-15 21:00:03,949 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_las: cmd_1752627603942
2025-07-15 21:00:03,949 - basketball_context_repair_system - INFO - [OK] federated_team_las: 0.000 ? 1.000
2025-07-15 21:00:04,028 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_las: trigger_expert_validation
2025-07-15 21:00:04,028 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_las
2025-07-15 21:00:07,964 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_lv
2025-07-15 21:00:07,971 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_lv: basketball_context_repair (Priority: 1)
2025-07-15 21:00:07,977 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_lv: integrate_basketball_context (Priority: 1)
2025-07-15 21:00:08,044 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_lv: basketball_context_repair
2025-07-15 21:00:08,044 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_lv
2025-07-15 21:00:08,145 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_lv: integrate_basketball_context
2025-07-15 21:00:08,145 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_lv
2025-07-15 21:00:09,001 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:09,001 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:09,046 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:09,046 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:09,195 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:09,195 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:12,944 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:12,944 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:13,107 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:13,565 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 21:00:13,766 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 21:00:13,766 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 21:00:15,018 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_lv: 1.000 (PRODUCTION READY)
2025-07-15 21:00:15,018 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_lv (score: 1.000)
2025-07-15 21:00:15,036 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_lv: trigger_expert_validation (Priority: 1)
2025-07-15 21:00:15,037 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_lv: cmd_1752627615019
2025-07-15 21:00:15,049 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_lv (score: 1.000)
2025-07-15 21:00:15,067 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_lv: trigger_expert_validation (Priority: 1)
2025-07-15 21:00:15,068 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_lv: cmd_1752627615050
2025-07-15 21:00:15,069 - basketball_context_repair_system - INFO - [OK] federated_team_lv: 0.000 ? 1.000
2025-07-15 21:00:15,071 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_lv: trigger_expert_validation
2025-07-15 21:00:15,074 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_lv
2025-07-15 21:00:15,178 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_lv: trigger_expert_validation
2025-07-15 21:00:15,178 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_lv
2025-07-15 21:00:19,101 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_min
2025-07-15 21:00:19,124 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_min: basketball_context_repair (Priority: 1)
2025-07-15 21:00:19,140 - military_grade_wnba_scraper - INFO - ?? APPLYING SOLUTION: generic_retry for extraction error
2025-07-15 21:00:19,141 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_min: integrate_basketball_context (Priority: 1)
2025-07-15 21:00:19,142 - military_grade_wnba_scraper - INFO - ?? FALLBACK: Using basic extraction for FanDuel
2025-07-15 21:00:19,197 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_min: basketball_context_repair
2025-07-15 21:00:19,198 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_min
2025-07-15 21:00:19,203 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:19,203 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:19,214 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:19,214 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:19,299 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_min: integrate_basketball_context
2025-07-15 21:00:19,299 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_min
2025-07-15 21:00:19,341 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:19,380 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:23,114 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:23,115 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:23,327 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:23,514 - military_grade_wnba_scraper - INFO - ?? FALLBACK: Extracted 0 props using basic selectors
2025-07-15 21:00:23,770 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 21:00:23,965 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 21:00:23,966 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 21:00:25,592 - digital_immune_system - INFO - [DATA] CONSOLIDATED STATUS: 12/12 monitors active, Health: 69.8%
2025-07-15 21:00:25,956 - military_grade_wnba_scraper - INFO - [OK] FanDuel: 0 props collected in 38.25s (Quality: 0.00)
2025-07-15 21:00:25,960 - military_grade_wnba_scraper - INFO - [TARGET] MILITARY SCRAPING: BetMGM (Props) - Strategy: default
2025-07-15 21:00:26,177 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_min: 1.000 (PRODUCTION READY)
2025-07-15 21:00:26,178 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_min (score: 1.000)
2025-07-15 21:00:26,194 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_min: trigger_expert_validation (Priority: 1)
2025-07-15 21:00:26,196 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_min: cmd_1752627626179
2025-07-15 21:00:26,210 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_min (score: 1.000)
2025-07-15 21:00:26,220 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_min: trigger_expert_validation (Priority: 1)
2025-07-15 21:00:26,221 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_min: cmd_1752627626211
2025-07-15 21:00:26,222 - basketball_context_repair_system - INFO - [OK] federated_team_min: 0.000 ? 1.000
2025-07-15 21:00:26,240 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_min: trigger_expert_validation
2025-07-15 21:00:26,241 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_min
2025-07-15 21:00:26,343 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_min: trigger_expert_validation
2025-07-15 21:00:26,344 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_min
2025-07-15 21:00:27,207 - military_grade_wnba_scraper - INFO - ?? Stealth driver created: 1866x992
2025-07-15 21:00:27,245 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:27,260 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:27,502 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:27,526 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:27,526 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:27,563 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:27,563 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:27,609 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:27,611 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:27,661 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:27,703 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:27,763 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:27,879 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:27,879 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:27,894 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:27,894 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:28,019 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:28,021 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:29,354 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:29,354 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:29,386 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:29,387 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:29,544 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:29,552 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:30,249 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_nyl
2025-07-15 21:00:30,261 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_nyl: basketball_context_repair (Priority: 1)
2025-07-15 21:00:30,267 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_nyl: integrate_basketball_context (Priority: 1)
2025-07-15 21:00:30,275 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_nyl: basketball_context_repair
2025-07-15 21:00:30,276 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_nyl
2025-07-15 21:00:30,377 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_nyl: integrate_basketball_context
2025-07-15 21:00:30,378 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_nyl
2025-07-15 21:00:30,995 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:30,996 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:31,120 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:31,417 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:31,418 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:31,435 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:31,436 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:31,549 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:31,596 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:33,337 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:33,338 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:33,466 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:33,974 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 21:00:34,218 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 21:00:34,219 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 21:00:34,998 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_server: health_check (Priority: 5)
2025-07-15 21:00:35,002 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_server: health_check
2025-07-15 21:00:35,003 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_server
2025-07-15 21:00:35,012 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_atl: health_check (Priority: 5)
2025-07-15 21:00:35,022 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_chi: health_check (Priority: 5)
2025-07-15 21:00:35,042 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_con: health_check (Priority: 5)
2025-07-15 21:00:35,052 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_dal: health_check (Priority: 5)
2025-07-15 21:00:35,068 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_gsv: health_check (Priority: 5)
2025-07-15 21:00:35,077 - digital_immune_system - INFO - [DATA] CONSOLIDATED STATUS: 12/12 monitors active, Health: 69.7%
2025-07-15 21:00:35,105 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_ind: health_check (Priority: 5)
2025-07-15 21:00:35,125 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_atl: health_check
2025-07-15 21:00:35,137 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_atl
2025-07-15 21:00:35,143 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_las: health_check (Priority: 5)
2025-07-15 21:00:35,169 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_lv: health_check (Priority: 5)
2025-07-15 21:00:35,185 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_min: health_check (Priority: 5)
2025-07-15 21:00:35,207 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_nyl: health_check (Priority: 5)
2025-07-15 21:00:35,229 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_pho: health_check (Priority: 5)
2025-07-15 21:00:35,238 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_con: health_check
2025-07-15 21:00:35,238 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_con
2025-07-15 21:00:35,239 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_sea: health_check (Priority: 5)
2025-07-15 21:00:35,258 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_was: health_check (Priority: 5)
2025-07-15 21:00:35,271 - autopilot_model_communication_layer - INFO - [SEND] Command sent to possession_based_model: health_check (Priority: 5)
2025-07-15 21:00:35,281 - autopilot_model_communication_layer - INFO - [SEND] Command sent to lineup_chemistry_model: health_check (Priority: 5)
2025-07-15 21:00:35,292 - autopilot_model_communication_layer - INFO - [SEND] Command sent to arena_effect_model: health_check (Priority: 5)
2025-07-15 21:00:35,313 - autopilot_model_communication_layer - INFO - [SEND] Command sent to team_dynamics_model: health_check (Priority: 5)
2025-07-15 21:00:35,332 - autopilot_model_communication_layer - INFO - [SEND] Command sent to contextual_performance_model: health_check (Priority: 5)
2025-07-15 21:00:35,340 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_ind: health_check
2025-07-15 21:00:35,341 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_ind
2025-07-15 21:00:35,350 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_multiverse_ensemble: health_check (Priority: 5)
2025-07-15 21:00:35,369 - autopilot_model_communication_layer - INFO - [SEND] Command sent to player_rebounds_model: health_check (Priority: 5)
2025-07-15 21:00:35,395 - autopilot_model_communication_layer - INFO - [SEND] Command sent to player_assists_model: health_check (Priority: 5)
2025-07-15 21:00:35,407 - autopilot_model_communication_layer - INFO - [SEND] Command sent to player_threes_model: health_check (Priority: 5)
2025-07-15 21:00:35,442 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_lv: health_check
2025-07-15 21:00:35,443 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_lv
2025-07-15 21:00:35,545 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_sea: health_check
2025-07-15 21:00:35,545 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_sea
2025-07-15 21:00:35,646 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to team_dynamics_model: health_check
2025-07-15 21:00:35,646 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> team_dynamics_model
2025-07-15 21:00:35,747 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_multiverse_ensemble: health_check
2025-07-15 21:00:35,751 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_multiverse_ensemble
2025-07-15 21:00:35,775 - military_grade_wnba_scraper - INFO - ?? APPLYING SOLUTION: generic_retry for extraction error
2025-07-15 21:00:35,776 - military_grade_wnba_scraper - INFO - ?? FALLBACK: Using basic extraction for BetMGM
2025-07-15 21:00:35,856 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to player_threes_model: health_check
2025-07-15 21:00:35,857 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> player_threes_model
2025-07-15 21:00:35,960 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to player_assists_model: health_check
2025-07-15 21:00:35,960 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> player_assists_model
2025-07-15 21:00:36,062 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to player_rebounds_model: health_check
2025-07-15 21:00:36,064 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> player_rebounds_model
2025-07-15 21:00:36,166 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to arena_effect_model: health_check
2025-07-15 21:00:36,167 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> arena_effect_model
2025-07-15 21:00:36,269 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to contextual_performance_model: health_check
2025-07-15 21:00:36,270 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> contextual_performance_model
2025-07-15 21:00:36,371 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to lineup_chemistry_model: health_check
2025-07-15 21:00:36,372 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> lineup_chemistry_model
2025-07-15 21:00:36,473 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to possession_based_model: health_check
2025-07-15 21:00:36,474 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> possession_based_model
2025-07-15 21:00:36,575 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_was: health_check
2025-07-15 21:00:36,575 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_was
2025-07-15 21:00:36,676 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_pho: health_check
2025-07-15 21:00:36,676 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_pho
2025-07-15 21:00:36,777 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_nyl: health_check
2025-07-15 21:00:36,782 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_nyl
2025-07-15 21:00:36,886 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_las: health_check
2025-07-15 21:00:36,887 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_las
2025-07-15 21:00:36,987 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_min: health_check
2025-07-15 21:00:36,988 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_min
2025-07-15 21:00:37,089 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_gsv: health_check
2025-07-15 21:00:37,089 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_gsv
2025-07-15 21:00:37,190 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_chi: health_check
2025-07-15 21:00:37,191 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_chi
2025-07-15 21:00:37,293 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_dal: health_check
2025-07-15 21:00:37,294 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_nyl: 1.000 (PRODUCTION READY)
2025-07-15 21:00:37,295 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_dal
2025-07-15 21:00:37,296 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_nyl (score: 1.000)
2025-07-15 21:00:37,315 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_nyl: trigger_expert_validation (Priority: 1)
2025-07-15 21:00:37,316 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_nyl: cmd_1752627637297
2025-07-15 21:00:37,330 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_nyl (score: 1.000)
2025-07-15 21:00:37,351 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_nyl: trigger_expert_validation (Priority: 1)
2025-07-15 21:00:37,353 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_nyl: cmd_1752627637331
2025-07-15 21:00:37,354 - basketball_context_repair_system - INFO - [OK] federated_team_nyl: 0.000 ? 1.000
2025-07-15 21:00:37,397 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_nyl: trigger_expert_validation
2025-07-15 21:00:37,398 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_nyl
2025-07-15 21:00:37,499 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_nyl: trigger_expert_validation
2025-07-15 21:00:37,500 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_nyl
2025-07-15 21:00:38,941 - military_grade_wnba_scraper - INFO - ?? FALLBACK: Extracted 0 props using basic selectors
2025-07-15 21:00:39,558 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:39,558 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:39,569 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:39,569 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:39,713 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:39,714 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:41,221 - military_grade_wnba_scraper - INFO - [OK] BetMGM: 0 props collected in 15.26s (Quality: 0.00)
2025-07-15 21:00:41,225 - military_grade_wnba_scraper - INFO - ? PHASE 2: Military-grade news scraping
2025-07-15 21:00:41,226 - military_grade_wnba_scraper - INFO - ? MILITARY NEWS SCRAPING: WNBA Official
2025-07-15 21:00:41,376 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_pho
2025-07-15 21:00:41,386 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_pho: basketball_context_repair (Priority: 1)
2025-07-15 21:00:41,402 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_pho: integrate_basketball_context (Priority: 1)
2025-07-15 21:00:41,419 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_pho: basketball_context_repair
2025-07-15 21:00:41,420 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_pho
2025-07-15 21:00:41,523 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_pho: integrate_basketball_context
2025-07-15 21:00:41,524 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_pho
2025-07-15 21:00:41,643 - military_grade_wnba_scraper - INFO - ? WNBA Official: 9 news items extracted
2025-07-15 21:00:41,652 - military_grade_wnba_scraper - INFO - [SAVE] Saved 9 news items to database
2025-07-15 21:00:41,653 - military_grade_wnba_scraper - INFO - [OK] WNBA Official: 9 news items collected in 0.43s
2025-07-15 21:00:41,654 - military_grade_wnba_scraper - INFO - ? MILITARY NEWS SCRAPING: ESPN WNBA
2025-07-15 21:00:41,942 - military_grade_wnba_scraper - INFO - ? ESPN WNBA: 0 news items extracted
2025-07-15 21:00:41,942 - military_grade_wnba_scraper - INFO - [OK] ESPN WNBA: 0 news items collected in 0.29s
2025-07-15 21:00:41,943 - military_grade_wnba_scraper - INFO - ? MILITARY NEWS SCRAPING: The Athletic WNBA
2025-07-15 21:00:42,481 - military_grade_wnba_scraper - INFO - ? The Athletic: 0 news items extracted
2025-07-15 21:00:42,481 - military_grade_wnba_scraper - INFO - [OK] The Athletic WNBA: 0 news items collected in 0.54s
2025-07-15 21:00:42,487 - military_grade_wnba_scraper - INFO - [EXPERT] MILITARY MISSION COMPLETE: Session 45195d6b84db
2025-07-15 21:00:42,487 - military_grade_wnba_scraper - INFO - [OK] Success Rate: 66.7%
2025-07-15 21:00:42,487 - military_grade_wnba_scraper - INFO - [TARGET] Props Collected: 2
2025-07-15 21:00:42,488 - military_grade_wnba_scraper - INFO - ? News Collected: 9
2025-07-15 21:00:42,488 - military_grade_wnba_scraper - INFO - ? Human Behavior Score: 0.62
2025-07-15 21:00:42,488 - military_grade_wnba_scraper - INFO - ?? Execution Time: 135.65s
2025-07-15 21:00:43,477 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:43,478 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:43,662 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:44,227 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 21:00:44,458 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 21:00:44,458 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 21:00:46,599 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000231545BE9C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/444ed4b9da417e1c624aef2f4e68849c
2025-07-15 21:00:48,439 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_pho: 1.000 (PRODUCTION READY)
2025-07-15 21:00:48,440 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_pho (score: 1.000)
2025-07-15 21:00:48,453 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_pho: trigger_expert_validation (Priority: 1)
2025-07-15 21:00:48,454 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_pho: cmd_1752627648441
2025-07-15 21:00:48,465 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_pho (score: 1.000)
2025-07-15 21:00:48,479 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_pho: trigger_expert_validation (Priority: 1)
2025-07-15 21:00:48,480 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_pho: cmd_1752627648465
2025-07-15 21:00:48,481 - basketball_context_repair_system - INFO - [OK] federated_team_pho: 0.000 ? 1.000
2025-07-15 21:00:48,494 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_pho: trigger_expert_validation
2025-07-15 21:00:48,494 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_pho
2025-07-15 21:00:48,595 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_pho: trigger_expert_validation
2025-07-15 21:00:48,596 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_pho
2025-07-15 21:00:49,726 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:49,726 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:49,734 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:49,735 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:49,922 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:49,923 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:50,692 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000231545BF460>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/444ed4b9da417e1c624aef2f4e68849c
2025-07-15 21:00:52,494 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_sea
2025-07-15 21:00:52,513 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_sea: basketball_context_repair
2025-07-15 21:00:52,514 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_sea
2025-07-15 21:00:52,518 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_sea: basketball_context_repair (Priority: 1)
2025-07-15 21:00:52,530 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_sea: integrate_basketball_context (Priority: 1)
2025-07-15 21:00:52,616 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_sea: integrate_basketball_context
2025-07-15 21:00:52,617 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_sea
2025-07-15 21:00:53,672 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:53,673 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:53,898 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:54,465 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 21:00:54,770 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000231545BC490>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/444ed4b9da417e1c624aef2f4e68849c
2025-07-15 21:00:54,868 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 21:00:54,870 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 21:00:57,711 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:57,712 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:57,775 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:57,776 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:57,882 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:57,918 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:58,027 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:58,028 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:58,038 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:58,039 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:58,233 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:58,468 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:00:59,567 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_sea: 1.000 (PRODUCTION READY)
2025-07-15 21:00:59,569 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_sea (score: 1.000)
2025-07-15 21:00:59,588 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_sea: trigger_expert_validation (Priority: 1)
2025-07-15 21:00:59,588 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_sea: cmd_1752627659570
2025-07-15 21:00:59,602 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_sea (score: 1.000)
2025-07-15 21:00:59,617 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_sea: trigger_expert_validation (Priority: 1)
2025-07-15 21:00:59,618 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_sea: cmd_1752627659602
2025-07-15 21:00:59,619 - basketball_context_repair_system - INFO - [OK] federated_team_sea: 0.000 ? 1.000
2025-07-15 21:00:59,649 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_sea: trigger_expert_validation
2025-07-15 21:00:59,651 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_sea
2025-07-15 21:00:59,753 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_sea: trigger_expert_validation
2025-07-15 21:00:59,755 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: trigger_expert_validation -> federated_team_sea
2025-07-15 21:00:59,931 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:59,931 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:00:59,941 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:00:59,942 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:01:00,105 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:01:00,106 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:01:01,558 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:01:01,559 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:01:01,606 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:01:01,606 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:01:01,683 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:01:01,760 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:01:03,003 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000023153259590>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b8bf15c446a8046d2dbfe0ba01503806
2025-07-15 21:01:03,640 - basketball_context_repair_system - INFO - [REPAIR] Repairing basketball context for: federated_team_was
2025-07-15 21:01:03,654 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_was: basketball_context_repair (Priority: 1)
2025-07-15 21:01:03,664 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_was: integrate_basketball_context (Priority: 1)
2025-07-15 21:01:03,674 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_was: basketball_context_repair
2025-07-15 21:01:03,675 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: basketball_context_repair -> federated_team_was
2025-07-15 21:01:03,776 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_was: integrate_basketball_context
2025-07-15 21:01:03,777 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: integrate_basketball_context -> federated_team_was
2025-07-15 21:01:03,907 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:01:03,907 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:01:04,096 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:01:04,879 - real_wnba_data_fetcher - INFO - [LIVE] Fetching live WNBA games...
2025-07-15 21:01:05,120 - real_wnba_data_fetcher - INFO - [LIVE] ESPN: Found 1 live games
2025-07-15 21:01:05,120 - win_probability_system - INFO - [LIVE] Found 1 live WNBA games
2025-07-15 21:01:05,416 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_server: health_check (Priority: 5)
2025-07-15 21:01:05,424 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_atl: health_check (Priority: 5)
2025-07-15 21:01:05,433 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_chi: health_check (Priority: 5)
2025-07-15 21:01:05,443 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_con: health_check (Priority: 5)
2025-07-15 21:01:05,450 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_dal: health_check (Priority: 5)
2025-07-15 21:01:05,459 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_gsv: health_check (Priority: 5)
2025-07-15 21:01:05,467 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_ind: health_check (Priority: 5)
2025-07-15 21:01:05,476 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_las: health_check (Priority: 5)
2025-07-15 21:01:05,485 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_server: health_check
2025-07-15 21:01:05,485 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_server
2025-07-15 21:01:05,486 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_lv: health_check (Priority: 5)
2025-07-15 21:01:05,493 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_min: health_check (Priority: 5)
2025-07-15 21:01:05,502 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_nyl: health_check (Priority: 5)
2025-07-15 21:01:05,510 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_pho: health_check (Priority: 5)
2025-07-15 21:01:05,519 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_sea: health_check (Priority: 5)
2025-07-15 21:01:05,525 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_was: health_check (Priority: 5)
2025-07-15 21:01:05,531 - autopilot_model_communication_layer - INFO - [SEND] Command sent to possession_based_model: health_check (Priority: 5)
2025-07-15 21:01:05,538 - autopilot_model_communication_layer - INFO - [SEND] Command sent to lineup_chemistry_model: health_check (Priority: 5)
2025-07-15 21:01:05,542 - autopilot_model_communication_layer - INFO - [SEND] Command sent to arena_effect_model: health_check (Priority: 5)
2025-07-15 21:01:05,548 - autopilot_model_communication_layer - INFO - [SEND] Command sent to team_dynamics_model: health_check (Priority: 5)
2025-07-15 21:01:05,555 - autopilot_model_communication_layer - INFO - [SEND] Command sent to contextual_performance_model: health_check (Priority: 5)
2025-07-15 21:01:05,561 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_multiverse_ensemble: health_check (Priority: 5)
2025-07-15 21:01:05,567 - autopilot_model_communication_layer - INFO - [SEND] Command sent to player_rebounds_model: health_check (Priority: 5)
2025-07-15 21:01:05,576 - autopilot_model_communication_layer - INFO - [SEND] Command sent to player_assists_model: health_check (Priority: 5)
2025-07-15 21:01:05,583 - autopilot_model_communication_layer - INFO - [SEND] Command sent to player_threes_model: health_check (Priority: 5)
2025-07-15 21:01:05,586 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_chi: health_check
2025-07-15 21:01:05,587 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_chi
2025-07-15 21:01:05,588 - autopilot_model_communication_layer - INFO - [SEND] Command sent to player_double_double_model: health_check (Priority: 5)
2025-07-15 21:01:05,595 - autopilot_model_communication_layer - INFO - [SEND] Command sent to pregame_win_probability_model: health_check (Priority: 5)
2025-07-15 21:01:05,601 - autopilot_model_communication_layer - INFO - [SEND] Command sent to live_win_probability_model: health_check (Priority: 5)
2025-07-15 21:01:05,607 - autopilot_model_communication_layer - INFO - [SEND] Command sent to enhanced_player_points_model: health_check (Priority: 5)
2025-07-15 21:01:05,613 - autopilot_model_communication_layer - INFO - [SEND] Command sent to hybrid_gnn_model: health_check (Priority: 5)
2025-07-15 21:01:05,620 - autopilot_model_communication_layer - INFO - [SEND] Command sent to multitask_model: health_check (Priority: 5)
2025-07-15 21:01:05,629 - autopilot_model_communication_layer - INFO - [SEND] Command sent to bayesian_model: health_check (Priority: 5)
2025-07-15 21:01:05,636 - autopilot_model_communication_layer - INFO - [SEND] Command sent to upset_prediction_model: health_check (Priority: 5)
2025-07-15 21:01:05,644 - autopilot_model_communication_layer - INFO - [SEND] Command sent to injury_impact_model: health_check (Priority: 5)
2025-07-15 21:01:05,650 - autopilot_model_communication_layer - INFO - [SEND] Command sent to momentum_analysis_model: health_check (Priority: 5)
2025-07-15 21:01:05,656 - autopilot_model_communication_layer - INFO - [SEND] Command sent to real_time_betting_intelligence: health_check (Priority: 5)
2025-07-15 21:01:05,664 - autopilot_model_communication_layer - INFO - [SEND] Command sent to win_probability_system: health_check (Priority: 5)
2025-07-15 21:01:05,671 - autopilot_model_communication_layer - INFO - [SEND] Command sent to live_espn_nba_api_integration: health_check (Priority: 5)
2025-07-15 21:01:05,679 - autopilot_model_communication_layer - INFO - [SEND] Command sent to live_nba_api_system: health_check (Priority: 5)
2025-07-15 21:01:05,686 - autopilot_model_communication_layer - INFO - [SEND] Command sent to live_espn_system: health_check (Priority: 5)
2025-07-15 21:01:05,688 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_ind: health_check
2025-07-15 21:01:05,688 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_ind
2025-07-15 21:01:05,690 - autopilot_model_communication_layer - INFO - [SEND] Command sent to hybrid_live_data_system: health_check (Priority: 5)
2025-07-15 21:01:05,694 - autopilot_model_communication_layer - INFO - [SEND] Command sent to elite_prediction_dashboard: health_check (Priority: 5)
2025-07-15 21:01:05,699 - autopilot_model_communication_layer - INFO - [SEND] Command sent to wnba_war_room_system: health_check (Priority: 5)
2025-07-15 21:01:05,704 - autopilot_model_communication_layer - INFO - [SEND] Command sent to monitoring_web_dashboard: health_check (Priority: 5)
2025-07-15 21:01:05,710 - autopilot_model_communication_layer - INFO - [SEND] Command sent to supreme_autopilot_system: health_check (Priority: 5)
2025-07-15 21:01:05,716 - autopilot_model_communication_layer - INFO - [SEND] Command sent to medusa_autopilot: health_check (Priority: 5)
2025-07-15 21:01:05,721 - autopilot_model_communication_layer - INFO - [SEND] Command sent to model_performance_autopilot: health_check (Priority: 5)
2025-07-15 21:01:05,727 - autopilot_model_communication_layer - INFO - [SEND] Command sent to system_health_monitor: health_check (Priority: 5)
2025-07-15 21:01:05,789 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_lv: health_check
2025-07-15 21:01:05,789 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_lv
2025-07-15 21:01:05,890 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to lineup_chemistry_model: health_check
2025-07-15 21:01:05,890 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> lineup_chemistry_model
2025-07-15 21:01:05,991 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to player_double_double_model: health_check
2025-07-15 21:01:05,991 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> player_double_double_model
2025-07-15 21:01:06,092 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to momentum_analysis_model: health_check
2025-07-15 21:01:06,092 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> momentum_analysis_model
2025-07-15 21:01:06,192 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to hybrid_live_data_system: health_check
2025-07-15 21:01:06,193 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> hybrid_live_data_system
2025-07-15 21:01:06,294 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to system_health_monitor: health_check
2025-07-15 21:01:06,294 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> system_health_monitor
2025-07-15 21:01:06,395 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to model_performance_autopilot: health_check
2025-07-15 21:01:06,395 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> model_performance_autopilot
2025-07-15 21:01:06,496 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to medusa_autopilot: health_check
2025-07-15 21:01:06,497 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> medusa_autopilot
2025-07-15 21:01:06,598 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to supreme_autopilot_system: health_check
2025-07-15 21:01:06,599 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> supreme_autopilot_system
2025-07-15 21:01:06,699 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to monitoring_web_dashboard: health_check
2025-07-15 21:01:06,700 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> monitoring_web_dashboard
2025-07-15 21:01:06,801 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to wnba_war_room_system: health_check
2025-07-15 21:01:06,801 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> wnba_war_room_system
2025-07-15 21:01:06,902 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to elite_prediction_dashboard: health_check
2025-07-15 21:01:06,902 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> elite_prediction_dashboard
2025-07-15 21:01:07,003 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to live_espn_system: health_check
2025-07-15 21:01:07,003 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> live_espn_system
2025-07-15 21:01:07,099 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000023147C7C270>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/b8bf15c446a8046d2dbfe0ba01503806
2025-07-15 21:01:07,104 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to live_nba_api_system: health_check
2025-07-15 21:01:07,105 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> live_nba_api_system
2025-07-15 21:01:07,205 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to live_espn_nba_api_integration: health_check
2025-07-15 21:01:07,206 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> live_espn_nba_api_integration
2025-07-15 21:01:07,307 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to win_probability_system: health_check
2025-07-15 21:01:07,308 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> win_probability_system
2025-07-15 21:01:07,409 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to injury_impact_model: health_check
2025-07-15 21:01:07,410 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> injury_impact_model
2025-07-15 21:01:07,510 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to real_time_betting_intelligence: health_check
2025-07-15 21:01:07,511 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> real_time_betting_intelligence
2025-07-15 21:01:07,611 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to upset_prediction_model: health_check
2025-07-15 21:01:07,612 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> upset_prediction_model
2025-07-15 21:01:07,713 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to bayesian_model: health_check
2025-07-15 21:01:07,714 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> bayesian_model
2025-07-15 21:01:07,815 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to multitask_model: health_check
2025-07-15 21:01:07,815 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> multitask_model
2025-07-15 21:01:07,916 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to hybrid_gnn_model: health_check
2025-07-15 21:01:07,917 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> hybrid_gnn_model
2025-07-15 21:01:08,018 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to enhanced_player_points_model: health_check
2025-07-15 21:01:08,019 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> enhanced_player_points_model
2025-07-15 21:01:08,120 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to live_win_probability_model: health_check
2025-07-15 21:01:08,121 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> live_win_probability_model
2025-07-15 21:01:08,222 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to pregame_win_probability_model: health_check
2025-07-15 21:01:08,223 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> pregame_win_probability_model
2025-07-15 21:01:08,325 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to player_threes_model: health_check
2025-07-15 21:01:08,326 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> player_threes_model
2025-07-15 21:01:08,428 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to player_assists_model: health_check
2025-07-15 21:01:08,429 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> player_assists_model
2025-07-15 21:01:08,531 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to player_rebounds_model: health_check
2025-07-15 21:01:08,532 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> player_rebounds_model
2025-07-15 21:01:08,632 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_multiverse_ensemble: health_check
2025-07-15 21:01:08,634 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_multiverse_ensemble
2025-07-15 21:01:08,735 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to contextual_performance_model: health_check
2025-07-15 21:01:08,736 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> contextual_performance_model
2025-07-15 21:01:08,837 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to team_dynamics_model: health_check
2025-07-15 21:01:08,838 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> team_dynamics_model
2025-07-15 21:01:08,939 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to possession_based_model: health_check
2025-07-15 21:01:08,940 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> possession_based_model
2025-07-15 21:01:09,040 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to arena_effect_model: health_check
2025-07-15 21:01:09,041 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> arena_effect_model
2025-07-15 21:01:09,142 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_was: health_check
2025-07-15 21:01:09,143 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_was
2025-07-15 21:01:09,244 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_sea: health_check
2025-07-15 21:01:09,245 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_sea
2025-07-15 21:01:09,346 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_pho: health_check
2025-07-15 21:01:09,347 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_pho
2025-07-15 21:01:09,448 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_nyl: health_check
2025-07-15 21:01:09,449 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_nyl
2025-07-15 21:01:09,550 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_min: health_check
2025-07-15 21:01:09,550 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_min
2025-07-15 21:01:09,651 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_gsv: health_check
2025-07-15 21:01:09,652 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_gsv
2025-07-15 21:01:09,753 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_las: health_check
2025-07-15 21:01:09,754 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_las
2025-07-15 21:01:09,855 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_dal: health_check
2025-07-15 21:01:09,856 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_dal
2025-07-15 21:01:09,957 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_atl: health_check
2025-07-15 21:01:09,958 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_atl
2025-07-15 21:01:10,059 - autopilot_model_communication_layer - INFO - [COMM] HTTP command sent to federated_team_con: health_check
2025-07-15 21:01:10,060 - autopilot_model_communication_layer - INFO - [EXEC] Command executed: health_check -> federated_team_con
2025-07-15 21:01:10,117 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:01:10,117 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:01:10,133 - nba_api_connector - INFO - [WNBA] NBA API Connector initialized for WNBA data
2025-07-15 21:01:10,134 - nba_api_connector - INFO - [DATE] Fetching WNBA games for 2025-07-15 from NBA API...
2025-07-15 21:01:10,318 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:01:10,343 - supreme_cache_management - INFO - [OK] NBA API: Found 2 games today
2025-07-15 21:01:10,690 - basketball_context_repair_system - INFO - [TARGET] FORCED basketball context score for federated_team_was: 1.000 (PRODUCTION READY)
2025-07-15 21:01:10,690 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_was (score: 1.000)
2025-07-15 21:01:10,698 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_was: trigger_expert_validation (Priority: 1)
2025-07-15 21:01:10,699 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_was: cmd_1752627670691
2025-07-15 21:01:10,704 - basketball_context_repair_system - INFO - [EXPERT] Triggering expert validation for federated_team_was (score: 1.000)
2025-07-15 21:01:10,711 - autopilot_model_communication_layer - INFO - [SEND] Command sent to federated_team_was: trigger_expert_validation (Priority: 1)
2025-07-15 21:01:10,711 - basketball_context_repair_system - INFO - [OK] Expert validation triggered for federated_team_was: cmd_1752627670705
2025-07-15 21:01:10,711 - basketball_context_repair_system - INFO - [OK] federated_team_was: 0.000 ? 1.000
