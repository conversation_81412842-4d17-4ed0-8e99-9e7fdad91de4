@echo off
REM 📅 INSTALL DAILY MILITARY SCHEDULER
REM ===================================
REM Windows batch script to install the daily scheduler as a service

echo 🎖️ INSTALLING DAILY MILITARY SCHEDULER
echo =====================================

REM Install required Python packages
echo 📦 Installing required packages...
pip install schedule psutil

REM Create Windows Task Scheduler entry
echo 📅 Creating Windows scheduled task...

REM Delete existing task if it exists
schtasks /delete /tn "WNBA_Military_Scheduler" /f >nul 2>&1

REM Create new scheduled task to run at startup and stay running
schtasks /create /tn "WNBA_Military_Scheduler" /tr "python \"%~dp0daily_military_scheduler.py\"" /sc onstart /ru SYSTEM /rl HIGHEST /f

if %errorlevel% equ 0 (
    echo ✅ Scheduled task created successfully
) else (
    echo ❌ Failed to create scheduled task
    echo 💡 Try running as Administrator
    pause
    exit /b 1
)

REM Create startup shortcut
echo 🚀 Creating startup shortcut...
set "startup_folder=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"
set "shortcut_path=%startup_folder%\WNBA_Military_Scheduler.lnk"

REM Create VBS script to create shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > create_shortcut.vbs
echo sLinkFile = "%shortcut_path%" >> create_shortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> create_shortcut.vbs
echo oLink.TargetPath = "python" >> create_shortcut.vbs
echo oLink.Arguments = """%~dp0daily_military_scheduler.py""" >> create_shortcut.vbs
echo oLink.WorkingDirectory = "%~dp0" >> create_shortcut.vbs
echo oLink.IconLocation = "python.exe" >> create_shortcut.vbs
echo oLink.Description = "WNBA Military-Grade Daily Scheduler" >> create_shortcut.vbs
echo oLink.Save >> create_shortcut.vbs

cscript create_shortcut.vbs >nul 2>&1
del create_shortcut.vbs

echo ✅ INSTALLATION COMPLETE
echo ======================
echo 📅 Daily scheduler will run automatically after 1:05 AM
echo 🎖️ Military-grade data collection scheduled
echo 🚀 Scheduler will start automatically on system boot
echo.
echo 💡 To start immediately, run: python daily_military_scheduler.py
echo 💡 To check status, open Task Scheduler and look for "WNBA_Military_Scheduler"
echo.
pause
