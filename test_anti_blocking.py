#!/usr/bin/env python3
"""
🛡️ TEST ANTI-BLOCKING MEASURES
==============================

Test the enhanced anti-blocking features of the prop scraper.
"""

import asyncio
import logging
from real_player_props_scraper import RealPlayerPropsScraper

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_anti_blocking():
    """Test anti-blocking measures"""
    print("🛡️ Testing Anti-Blocking Measures")
    print("=" * 40)
    
    scraper = RealPlayerPropsScraper()
    
    # Test 1: Driver creation with stealth features
    print("\n🚗 Test 1: Enhanced Driver Creation")
    driver = scraper._create_driver()
    if driver:
        print("✅ Chrome driver with anti-detection created")
        
        # Test stealth features
        user_agent = driver.execute_script("return navigator.userAgent;")
        webdriver_prop = driver.execute_script("return navigator.webdriver;")
        plugins = driver.execute_script("return navigator.plugins.length;")
        
        print(f"   • User Agent: {user_agent[:50]}...")
        print(f"   • WebDriver Property: {webdriver_prop}")
        print(f"   • Plugins Count: {plugins}")
        
        driver.quit()
    else:
        print("❌ Chrome driver creation failed")
    
    # Test 2: Firefox fallback
    print("\n🦊 Test 2: Firefox Fallback")
    firefox_driver = scraper._create_firefox_driver()
    if firefox_driver:
        print("✅ Firefox driver created successfully")
        firefox_driver.quit()
    else:
        print("❌ Firefox driver creation failed")
    
    # Test 3: Block detection
    print("\n🚫 Test 3: Block Detection")
    
    # Simulate blocked content
    test_cases = [
        ("Normal content", "Welcome to DraftKings WNBA betting", False),
        ("Blocked content", "Access denied - suspicious activity detected", True),
        ("Captcha content", "Please verify you are human with this captcha", True),
        ("Rate limit", "Too many requests from your IP address", True),
        ("Small content", "Error", True)
    ]
    
    for test_name, content, should_block in test_cases:
        # This would normally be tested with a real driver
        # For now, just show the test structure
        print(f"   • {test_name}: {'Would block' if should_block else 'Would allow'}")
    
    # Test 4: Alternative URL strategy
    print("\n🔄 Test 4: Alternative URLs")
    dk_config = scraper.sportsbooks['draftkings']
    print(f"   • Main URL: {dk_config['base_url']}")
    print(f"   • Prop URLs: {len(dk_config['prop_urls'])} categories")
    
    alt_urls = [
        'https://sportsbook.draftkings.com/leagues/basketball/88670846',
        'https://sportsbook.draftkings.com/sports/basketball/88670846',
        'https://sportsbook.draftkings.com/featured/wnba'
    ]
    
    for i, url in enumerate(alt_urls, 1):
        print(f"   • Alternative {i}: {url}")
    
    # Test 5: Requests fallback
    print("\n🌐 Test 5: Requests Fallback")
    try:
        import requests
        session = requests.Session()
        session.headers.update({
            'User-Agent': scraper.user_agents[0],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        })
        print("✅ Requests session configured for fallback")
        print(f"   • User agents available: {len(scraper.user_agents)}")
        print(f"   • Proxies configured: {len(scraper.proxies) if scraper.proxies else 0}")
    except ImportError:
        print("❌ Requests not available for fallback")
    
    # Test 6: Human-like delays
    print("\n⏱️ Test 6: Human-like Timing")
    delay_types = ['page_load', 'between_actions', 'between_sportsbooks', 'scroll_delay']
    
    for delay_type in delay_types:
        min_delay, max_delay = scraper.human_delays.get(delay_type, (1, 3))
        print(f"   • {delay_type}: {min_delay}-{max_delay} seconds")
    
    print("\n🎯 Anti-blocking test completed!")
    print("\nℹ️ To test with real blocking, run the main scraper and monitor logs")

if __name__ == "__main__":
    asyncio.run(test_anti_blocking())
