#!/usr/bin/env python3
"""
🤖 AUTOPILOT-MODEL COMMUNICATION LAYER
=====================================

Robust communication infrastructure between Supreme Autopilot and all models.
Enables real-time model control, updates, and coordination.

COMMUNICATION FEATURES:
🔄 Real-time Model Control
📊 Performance Monitoring
🎯 Dynamic Model Selection
⚡ Live Parameter Updates
🧠 Basketball Intelligence Sync
🔧 Model Health Monitoring
📈 Accuracy Feedback Loops
🚀 Autonomous Model Management

Author: WNBA Analytics Team
"""

import asyncio
import websockets
import json
import threading
import queue
import time
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
import sqlite3
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelCommand:
    """Command structure for model communication"""
    command_id: str
    model_id: str
    command_type: str  # predict, update, retrain, status, etc.
    parameters: Dict[str, Any]
    priority: int  # 1=highest, 5=lowest
    timestamp: str
    basketball_context: Optional[Dict[str, Any]] = None

    def __lt__(self, other):
        """Make ModelCommand comparable for priority queue"""
        return self.priority < other.priority

@dataclass
class ModelResponse:
    """Response structure from models"""
    response_id: str
    model_id: str
    command_id: str
    status: str  # success, error, processing
    data: Dict[str, Any]
    execution_time: float
    timestamp: str
    basketball_insights: Optional[List[str]] = None

class AutopilotModelCommunicationLayer:
    """🤖 Communication layer between autopilot and models"""
    
    def __init__(self):
        self.command_queue = queue.PriorityQueue()
        self.response_queue = queue.Queue()
        self.model_registry = {}
        self.active_connections = {}
        self.communication_active = True

        # Registered models for communication (fixes missing attribute error)
        self.registered_models = {}

        # Basketball intelligence integration
        self.basketball_context_cache = {}
        self.model_performance_cache = {}

        # WebSocket server for real-time communication
        self.websocket_port = 8765
        self.websocket_server = None
        self.websocket_enabled = True  # Will be set to False if WebSocket fails

        # Database for communication logging
        self._init_communication_database()
        
        # Start communication threads
        self._start_communication_threads()
        
        logger.info("🤖 Autopilot-Model Communication Layer initialized")
    
    def _init_communication_database(self):
        """Initialize communication logging database"""
        
        conn = sqlite3.connect('autopilot_model_communication.db')
        cursor = conn.cursor()
        
        # Model commands log
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_commands (
                id INTEGER PRIMARY KEY,
                command_id TEXT,
                model_id TEXT,
                command_type TEXT,
                parameters TEXT,
                priority INTEGER,
                timestamp TEXT,
                basketball_context TEXT
            )
        ''')
        
        # Model responses log
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_responses (
                id INTEGER PRIMARY KEY,
                response_id TEXT,
                model_id TEXT,
                command_id TEXT,
                status TEXT,
                data TEXT,
                execution_time REAL,
                timestamp TEXT,
                basketball_insights TEXT
            )
        ''')
        
        # Model performance tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_performance (
                id INTEGER PRIMARY KEY,
                model_id TEXT,
                timestamp TEXT,
                accuracy REAL,
                response_time REAL,
                basketball_context_score REAL,
                status TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("🗄️ Communication database initialized")
    
    def _start_communication_threads(self):
        """Start all communication threads"""
        
        # Command processing thread
        threading.Thread(target=self._command_processor, daemon=True).start()
        
        # Response handler thread
        threading.Thread(target=self._response_handler, daemon=True).start()
        
        # Model health monitor thread
        threading.Thread(target=self._model_health_monitor, daemon=True).start()
        
        # Basketball context sync thread
        threading.Thread(target=self._basketball_context_sync, daemon=True).start()
        
        # WebSocket server thread
        threading.Thread(target=self._start_websocket_server, daemon=True).start()
        
        logger.info("🚀 All communication threads started")
    
    def register_model(self, model_id: str, model_info: Dict[str, Any]):
        """Register a model with the communication layer"""
        
        self.model_registry[model_id] = {
            'info': model_info,
            'status': 'active',
            'last_seen': datetime.now().isoformat(),
            'performance_metrics': {
                'accuracy': 0.0,
                'response_time': 0.0,
                'basketball_context_score': 0.0
            }
        }
        
        logger.info(f"📝 Model registered: {model_id}")
    
    def send_command_to_model(self, model_id: str, command_type: str, 
                            parameters: Dict[str, Any], priority: int = 3,
                            basketball_context: Optional[Dict[str, Any]] = None) -> str:
        """Send command to specific model"""
        
        command_id = f"cmd_{int(time.time() * 1000)}"
        
        command = ModelCommand(
            command_id=command_id,
            model_id=model_id,
            command_type=command_type,
            parameters=parameters,
            priority=priority,
            timestamp=datetime.now().isoformat(),
            basketball_context=basketball_context
        )
        
        # Add to priority queue (lower number = higher priority)
        self.command_queue.put((priority, command))
        
        # Log command
        self._log_command(command)
        
        # Reduce logging volume - only log important commands
        if command_type not in ['reactivate', 'status_check']:
            logger.info(f"[SEND] Command sent to {model_id}: {command_type} (Priority: {priority})")
        else:
            logger.debug(f"[SEND] Command sent to {model_id}: {command_type} (Priority: {priority})")
        
        return command_id
    
    def send_basketball_context_update(self, context: Dict[str, Any]):
        """Send basketball context update to all models"""
        
        self.basketball_context_cache = context
        
        # Send to all active models
        for model_id in self.model_registry:
            if self.model_registry[model_id]['status'] == 'active':
                self.send_command_to_model(
                    model_id=model_id,
                    command_type='basketball_context_update',
                    parameters={'context': context},
                    priority=2  # High priority for context updates
                )
        
        logger.info("🏀 Basketball context update sent to all models")

    def send_injury_update(self, injury_context: Dict[str, Any]):
        """Send injury update to all registered models"""

        try:
            for model_id in self.registered_models:
                # Create injury update command
                command = ModelCommand(
                    command_id=f"injury_update_{int(time.time())}",
                    model_id=model_id,
                    command_type="injury_update",
                    parameters=injury_context,
                    priority=1,  # High priority for injury updates
                    timestamp=datetime.now(),
                    basketball_context=injury_context
                )

                # Send command
                self.send_command(command)
                logger.info(f"🏥 Injury update sent to {model_id}")

            logger.info("🏥 Injury updates sent to all models")

        except Exception as e:
            logger.error(f"❌ Error sending injury updates: {e}")
    
    def request_predictions(self, model_ids: List[str], input_data: Dict[str, Any],
                          basketball_context: Dict[str, Any]) -> Dict[str, str]:
        """Request predictions from multiple models"""
        
        command_ids = {}
        
        for model_id in model_ids:
            command_id = self.send_command_to_model(
                model_id=model_id,
                command_type='predict',
                parameters={'input_data': input_data},
                priority=1,  # Highest priority for predictions
                basketball_context=basketball_context
            )
            command_ids[model_id] = command_id
        
        logger.info(f"🎯 Prediction requests sent to {len(model_ids)} models")
        
        return command_ids
    
    def update_model_parameters(self, model_id: str, parameters: Dict[str, Any]):
        """Update model parameters dynamically"""
        
        command_id = self.send_command_to_model(
            model_id=model_id,
            command_type='update_parameters',
            parameters=parameters,
            priority=2
        )
        
        logger.info(f"🔧 Parameter update sent to {model_id}")
        
        return command_id
    
    def trigger_model_retraining(self, model_id: str, training_config: Dict[str, Any]):
        """Trigger model retraining"""
        
        command_id = self.send_command_to_model(
            model_id=model_id,
            command_type='retrain',
            parameters=training_config,
            priority=4  # Lower priority for retraining
        )
        
        logger.info(f"🔄 Retraining triggered for {model_id}")
        
        return command_id
    
    def get_model_status(self, model_id: str) -> Dict[str, Any]:
        """Get current status of a model"""
        
        if model_id in self.model_registry:
            return self.model_registry[model_id]
        else:
            return {'error': 'Model not found'}
    
    def get_all_model_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all registered models"""
        
        return self.model_registry.copy()
    
    def _command_processor(self):
        """Process commands from the queue"""
        
        while self.communication_active:
            try:
                if not self.command_queue.empty():
                    priority, command = self.command_queue.get()
                    
                    # Process command based on type
                    self._execute_command(command)
                    
                    # Mark task as done
                    self.command_queue.task_done()
                
                time.sleep(0.1)  # Small delay to prevent busy waiting
                
            except Exception as e:
                logger.error(f"❌ Command processor error: {e}")
                time.sleep(1)
    
    def _execute_command(self, command: ModelCommand):
        """Execute a command"""
        
        model_id = command.model_id
        
        if model_id not in self.model_registry:
            logger.warning(f"⚠️ Unknown model: {model_id}")
            return
        
        # Send command via WebSocket if connected
        if model_id in self.active_connections:
            self._send_websocket_command(model_id, command)
        else:
            # Fallback to HTTP or other communication method
            self._send_http_command(model_id, command)
        
        # Reduce logging volume - only log important commands
        if command.command_type not in ['reactivate', 'status_check']:
            logger.info(f"[EXEC] Command executed: {command.command_type} -> {model_id}")
        else:
            logger.debug(f"[EXEC] Command executed: {command.command_type} -> {model_id}")
    
    def _send_websocket_command(self, model_id: str, command: ModelCommand):
        """Send command via WebSocket"""
        
        try:
            websocket = self.active_connections[model_id]
            command_data = asdict(command)

            # Send command asynchronously with proper event loop handling
            try:
                # Try to get current event loop
                try:
                    loop = asyncio.get_running_loop()
                    # Create task in running loop
                    asyncio.create_task(websocket.send(json.dumps(command_data, default=str)))
                except RuntimeError:
                    # No running loop, try to get or create one
                    try:
                        loop = asyncio.get_event_loop()
                        if not loop.is_running():
                            loop.run_until_complete(websocket.send(json.dumps(command_data, default=str)))
                    except RuntimeError:
                        # No event loop available, skip WebSocket
                        logger.debug(f"⚠️ WebSocket unavailable for {model_id}: No event loop")
                        return
            except Exception as e:
                logger.debug(f"⚠️ WebSocket send error for {model_id}: {e}")

        except KeyError:
            logger.warning(f"⚠️ WebSocket connection not found for {model_id}")
        except Exception as e:
            logger.warning(f"⚠️ WebSocket send error for {model_id}: {e}")
    
    def _send_http_command(self, model_id: str, command: ModelCommand):
        """Send command via HTTP (fallback)"""
        
        # This would implement HTTP-based communication
        # Reduce logging volume - only log important commands
        if command.command_type not in ['reactivate', 'status_check']:
            logger.info(f"[COMM] HTTP command sent to {model_id}: {command.command_type}")
        else:
            logger.debug(f"[COMM] HTTP command sent to {model_id}: {command.command_type}")
    
    def _response_handler(self):
        """Handle responses from models"""
        
        while self.communication_active:
            try:
                if not self.response_queue.empty():
                    response = self.response_queue.get()
                    
                    # Process response
                    self._process_model_response(response)
                    
                    # Update model performance metrics
                    self._update_model_performance(response)
                
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"❌ Response handler error: {e}")
                time.sleep(1)
    
    def _process_model_response(self, response: ModelResponse):
        """Process a response from a model"""
        
        # Log response
        self._log_response(response)
        
        # Update model status
        if response.model_id in self.model_registry:
            self.model_registry[response.model_id]['last_seen'] = response.timestamp
            
            if response.status == 'error':
                self.model_registry[response.model_id]['status'] = 'error'
                logger.warning(f"⚠️ Model error: {response.model_id}")
            else:
                self.model_registry[response.model_id]['status'] = 'active'
        
        logger.info(f"📥 Response processed from {response.model_id}: {response.status}")
    
    def _update_model_performance(self, response: ModelResponse):
        """Update model performance metrics"""
        
        model_id = response.model_id
        
        if model_id in self.model_registry:
            metrics = self.model_registry[model_id]['performance_metrics']
            
            # Update response time
            metrics['response_time'] = response.execution_time
            
            # Update accuracy if available
            if 'accuracy' in response.data:
                metrics['accuracy'] = response.data['accuracy']
            
            # Update basketball context score
            if response.basketball_insights:
                metrics['basketball_context_score'] = len(response.basketball_insights) / 10.0
        
        # Log to database
        self._log_performance_metrics(model_id, metrics)
    
    def _model_health_monitor(self):
        """Monitor health of all registered models"""
        
        while self.communication_active:
            try:
                current_time = datetime.now()
                
                for model_id, model_info in self.model_registry.items():
                    last_seen = datetime.fromisoformat(model_info['last_seen'])
                    time_diff = (current_time - last_seen).total_seconds()
                    
                    # Mark as inactive if not seen for 10 minutes (increased from 5)
                    if time_diff > 600:
                        if model_info['status'] != 'inactive':
                            model_info['status'] = 'inactive'
                            logger.warning(f"⚠️ Model inactive: {model_id}")
                            # Attempt to reactivate the model
                            self._attempt_model_reactivation(model_id)

                        # Only log warning once per hour to reduce noise
                        last_warning = model_info.get('last_warning', 0)
                        if current_time.timestamp() - last_warning > 3600:
                            model_info['last_warning'] = current_time.timestamp()

                    # Send health check less frequently
                    if time_diff > 120:  # Send health check every 2 minutes (reduced frequency)
                        self.send_command_to_model(
                            model_id=model_id,
                            command_type='health_check',
                            parameters={},
                            priority=5
                        )
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"❌ Health monitor error: {e}")
                time.sleep(60)

    def _attempt_model_reactivation(self, model_id: str):
        """Attempt to reactivate an inactive model"""
        try:
            logger.info(f"🔄 Attempting to reactivate model: {model_id}")

            # Send reactivation command
            self.send_command_to_model(
                model_id=model_id,
                command_type='reactivate',
                parameters={
                    'timestamp': datetime.now().isoformat(),
                    'reason': 'health_check_failure'
                },
                priority=2  # High priority for reactivation
            )

            # Update model status
            if model_id in self.model_registry:
                self.model_registry[model_id]['status'] = 'reactivating'
                self.model_registry[model_id]['last_reactivation_attempt'] = datetime.now().isoformat()

        except Exception as e:
            logger.error(f"❌ Model reactivation failed for {model_id}: {e}")

    def _basketball_context_sync(self):
        """Sync basketball context across all models"""
        
        while self.communication_active:
            try:
                # Check if basketball context needs updating
                if self.basketball_context_cache:
                    # Send context update to models that need it
                    for model_id in self.model_registry:
                        if self.model_registry[model_id]['status'] == 'active':
                            # Check if model needs context update
                            self._check_and_send_context_update(model_id)
                
                time.sleep(60)  # Sync every minute
                
            except Exception as e:
                logger.error(f"❌ Basketball context sync error: {e}")
                time.sleep(120)
    
    def _check_and_send_context_update(self, model_id: str):
        """Check if model needs basketball context update"""
        
        # This would implement logic to determine if context update is needed
        # For now, send update periodically
        
        if self.basketball_context_cache:
            self.send_command_to_model(
                model_id=model_id,
                command_type='basketball_context_sync',
                parameters={'context': self.basketball_context_cache},
                priority=3
            )
    
    def _start_websocket_server(self):
        """Start WebSocket server for real-time communication"""
        
        async def handle_client(websocket, path):
            """Handle WebSocket client connection"""
            
            try:
                # Register connection
                model_id = await websocket.recv()
                self.active_connections[model_id] = websocket
                
                logger.info(f"🔌 WebSocket connected: {model_id}")
                
                # Handle messages
                async for message in websocket:
                    response_data = json.loads(message)
                    response = ModelResponse(**response_data)
                    self.response_queue.put(response)
                    
            except websockets.exceptions.ConnectionClosed:
                # Remove connection
                for mid, ws in list(self.active_connections.items()):
                    if ws == websocket:
                        del self.active_connections[mid]
                        logger.info(f"🔌 WebSocket disconnected: {mid}")
                        break
            except Exception as e:
                logger.error(f"❌ WebSocket error: {e}")
        
        # Start server with improved error handling
        try:
            # Check if websockets module is available
            import websockets

            # Improved event loop handling
            try:
                # Try to get existing event loop
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Event loop is closed")
            except RuntimeError:
                # Create new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Start WebSocket server
            start_server = websockets.serve(handle_client, "localhost", self.websocket_port)
            loop.run_until_complete(start_server)

            logger.info(f"🔌 WebSocket server started on port {self.websocket_port}")

            # Run event loop
            loop.run_forever()

        except ImportError:
            logger.info("ℹ️ WebSocket server disabled: Using HTTP communication only")
            self.websocket_enabled = False
        except RuntimeError as e:
            if "no running event loop" in str(e):
                logger.info("ℹ️ WebSocket server skipped: Using HTTP communication instead")
                self.websocket_enabled = False
            else:
                logger.warning(f"⚠️ WebSocket server unavailable: {e}")
                self.websocket_enabled = False
        except Exception as e:
            logger.info(f"ℹ️ WebSocket server unavailable, using HTTP only: {e}")
            self.websocket_enabled = False
    
    def _log_command(self, command: ModelCommand):
        """Log command to database with retry logic for locks"""
        max_retries = 3
        retry_delay = 0.1

        for attempt in range(max_retries):
            try:
                conn = sqlite3.connect('autopilot_model_communication.db', timeout=10.0)
                conn.execute("PRAGMA journal_mode=WAL")  # Enable WAL mode for better concurrency
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO model_commands
                    (command_id, model_id, command_type, parameters, priority, timestamp, basketball_context)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    command.command_id,
                    command.model_id,
                    command.command_type,
                    json.dumps(command.parameters, default=str),
                    command.priority,
                    command.timestamp.isoformat() if hasattr(command.timestamp, 'isoformat') else str(command.timestamp),
                    json.dumps(command.basketball_context, default=str) if command.basketball_context else None
                ))

                conn.commit()
                conn.close()
                return  # Success, exit retry loop

            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    time.sleep(retry_delay * (2 ** attempt))  # Exponential backoff
                    continue
                else:
                    logger.error(f"❌ Command logging error after {attempt + 1} attempts: {e}")
                    break
            except Exception as e:
                logger.error(f"❌ Command logging error: {e}")
                break
    
    def _log_response(self, response: ModelResponse):
        """Log response to database"""
        
        try:
            conn = sqlite3.connect('autopilot_model_communication.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO model_responses 
                (response_id, model_id, command_id, status, data, execution_time, timestamp, basketball_insights)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                response.response_id,
                response.model_id,
                response.command_id,
                response.status,
                json.dumps(response.data, default=str),
                response.execution_time,
                response.timestamp.isoformat() if hasattr(response.timestamp, 'isoformat') else str(response.timestamp),
                json.dumps(response.basketball_insights, default=str) if response.basketball_insights else None
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Response logging error: {e}")
    
    def _log_performance_metrics(self, model_id: str, metrics: Dict[str, float]):
        """Log performance metrics to database"""
        
        try:
            conn = sqlite3.connect('autopilot_model_communication.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO model_performance 
                (model_id, timestamp, accuracy, response_time, basketball_context_score, status)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                model_id,
                datetime.now().isoformat(),
                metrics.get('accuracy', 0.0),
                metrics.get('response_time', 0.0),
                metrics.get('basketball_context_score', 0.0),
                'active'
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Performance logging error: {e}")
    
    def shutdown(self):
        """Shutdown communication layer"""
        
        self.communication_active = False
        
        # Close all WebSocket connections
        for websocket in self.active_connections.values():
            asyncio.create_task(websocket.close())
        
        logger.info("🔌 Communication layer shutdown")

    def reset_all_connections(self):
        """Reset all model connections - used by Digital Immune System"""
        try:
            logger.info("🔄 Resetting all model connections...")

            # Clear active connections
            if hasattr(self, 'active_connections'):
                self.active_connections.clear()

            # Reset model registry
            if hasattr(self, 'model_registry'):
                for model_id in list(self.model_registry.keys()):
                    self.model_registry[model_id]['status'] = 'reconnecting'

            # Clear command queues
            while not self.command_queue.empty():
                try:
                    self.command_queue.get_nowait()
                except:
                    break

            while not self.response_queue.empty():
                try:
                    self.response_queue.get_nowait()
                except:
                    break

            logger.info("✅ All model connections reset successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to reset connections: {e}")
            return False

def main():
    """Demonstrate communication layer"""
    
    print("🤖 AUTOPILOT-MODEL COMMUNICATION LAYER")
    print("=" * 60)
    print("🔄 Real-time model control and coordination")
    print("🏀 Basketball intelligence synchronization")
    print()
    
    # Initialize communication layer
    comm_layer = AutopilotModelCommunicationLayer()
    
    # Register some example models
    comm_layer.register_model('player_points_model', {
        'type': 'prediction',
        'target': 'points',
        'basketball_aware': True
    })
    
    comm_layer.register_model('player_rebounds_model', {
        'type': 'prediction',
        'target': 'rebounds',
        'basketball_aware': True
    })
    
    # Send example commands
    basketball_context = {
        'clutch_time': True,
        'game_importance': 'playoff_game',
        'fatigue_level': 0.8
    }
    
    # Request predictions
    command_ids = comm_layer.request_predictions(
        model_ids=['player_points_model', 'player_rebounds_model'],
        input_data={'player_id': 'aja_wilson', 'minutes': 32},
        basketball_context=basketball_context
    )
    
    print("📤 Commands sent:")
    for model_id, command_id in command_ids.items():
        print(f"   {model_id}: {command_id}")
    
    # Show model status
    print("\n📊 Model Status:")
    status = comm_layer.get_all_model_status()
    for model_id, info in status.items():
        print(f"   {model_id}: {info['status']}")
    
    print("\n✅ Communication layer operational!")
    print("🔌 WebSocket server running on port 8765")
    print("🤖 Ready for autopilot-model communication!")
    
    # Keep running for demonstration
    time.sleep(5)
    
    comm_layer.shutdown()

# Global instance for easy access
_global_communication_layer = None

def get_autopilot_communication_layer():
    """Get the global autopilot communication layer instance"""
    global _global_communication_layer
    if _global_communication_layer is None:
        _global_communication_layer = AutopilotModelCommunicationLayer()
    return _global_communication_layer

def initialize_autopilot_communication():
    """Initialize the global communication layer"""
    global _global_communication_layer
    if _global_communication_layer is None:
        _global_communication_layer = AutopilotModelCommunicationLayer()
        logger.info("✅ Autopilot communication layer initialized")
    return _global_communication_layer

if __name__ == "__main__":
    main()
