#!/usr/bin/env python3
"""
🎯 REAL PLAYER PROPS SCRAPER
===========================

ACTUAL SCRAPING of player props from sportsbook websites since Odds API free tier doesn't include player props.

Features:
- Real web scraping from DraftKings, FanDuel, etc.
- WNBA player props extraction
- Anti-detection measures
- Fallback strategies
- Data validation

Author: WNBA Analytics Team
"""

import asyncio
import time
import json
import re
import random
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
from dataclasses import dataclass
import sqlite3
import requests
from itertools import cycle

# Selenium imports
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class PlayerProp:
    """Real player prop data"""
    player_name: str
    prop_type: str
    line: float
    over_odds: int
    under_odds: int
    sportsbook: str
    game_info: str
    timestamp: str

class RealPlayerPropsScraper:
    """Real scraper for WNBA player props"""
    
    def __init__(self):
        """Initialize expert props scraper with anti-detection"""

        # Expert user agents rotation
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36'
        ]

        # Expert proxy rotation - Premium services configuration
        self.proxies = self._configure_premium_proxies()
        self.proxy_cycle = cycle(self.proxies) if self.proxies else None

        # Human-like timing patterns
        self.human_delays = {
            'page_load': (3, 7),
            'between_actions': (1, 3),
            'between_sportsbooks': (5, 10),
            'scroll_delay': (0.5, 1.5)
        }
        self.sportsbooks = {
            'draftkings': {
                'base_url': 'https://sportsbook.draftkings.com/leagues/basketball/wnba',
                'prop_urls': {
                    'points': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-points&subcategory=points',
                    'rebounds': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-rebounds&subcategory=rebounds',
                    'assists': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-assists&subcategory=assists',
                    'threes': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=player-threes&subcategory=threes',
                    'h2h': 'https://sportsbook.draftkings.com/leagues/basketball/wnba?category=h2h-player-props&subcategory=h2h-points'
                },
                'name': 'DraftKings',
                'selectors': {
                    'prop_containers': [
                        '.sportsbook-outcome-cell',
                        '.outcome',
                        '[data-testid*="outcome"]',
                        '.sportsbook-table__body-row',
                        '.sportsbook-table-row',
                        '.sportsbook-outcome'
                    ],
                    'player_names': [
                        '.participant-name',
                        '.player-name',
                        '[data-testid*="player"]',
                        '.sportsbook-outcome-cell__label',
                        '.sportsbook-table__body-cell'
                    ],
                    'prop_lines': [
                        '.sportsbook-outcome-cell__line',
                        '.line',
                        '.prop-line',
                        '.sportsbook-outcome-cell__handicap'
                    ],
                    'odds': [
                        '.sportsbook-odds',
                        '.odds',
                        '[data-testid*="odds"]',
                        '.sportsbook-outcome-cell__odds'
                    ]
                }
            },
            'fanduel': {
                'base_url': 'https://sportsbook.fanduel.com/basketball/wnba',
                'prop_urls': {
                    'points': 'https://sportsbook.fanduel.com/basketball/wnba/player-props/points',
                    'rebounds': 'https://sportsbook.fanduel.com/basketball/wnba/player-props/rebounds',
                    'assists': 'https://sportsbook.fanduel.com/basketball/wnba/player-props/assists',
                    'threes': 'https://sportsbook.fanduel.com/basketball/wnba/player-props/threes-made',
                    'combo': 'https://sportsbook.fanduel.com/basketball/wnba/player-props/combo-props'
                },
                'name': 'FanDuel',
                'selectors': {
                    'prop_containers': [
                        '[data-test-id*="Market"]',
                        '.market-container',
                        '.prop-market',
                        '[data-test-id*="outcome-cell"]',
                        '.outcome-cell'
                    ],
                    'player_names': [
                        '[aria-label*="player"]',
                        '.player-name',
                        '.participant',
                        '[data-test-id*="player"]',
                        '.outcome-cell__label'
                    ],
                    'prop_lines': [
                        '.market-line',
                        '.prop-line',
                        '[data-test-id*="line"]',
                        '.outcome-cell__line'
                    ],
                    'odds': [
                        '[data-test-id*="odds"]',
                        '.odds-value',
                        '.price',
                        '.outcome-cell__odds'
                    ]
                }
            },
            'betmgm': {
                'base_url': 'https://www.nc.betmgm.com/en/sports/basketball-7/betting/usa-9/wnba-402',
                'prop_urls': {
                    'points': 'https://www.nc.betmgm.com/en/sports/basketball-7/betting/usa-9/wnba-402/player-props/points',
                    'rebounds': 'https://www.nc.betmgm.com/en/sports/basketball-7/betting/usa-9/wnba-402/player-props/rebounds',
                    'assists': 'https://www.nc.betmgm.com/en/sports/basketball-7/betting/usa-9/wnba-402/player-props/assists',
                    'threes': 'https://www.nc.betmgm.com/en/sports/basketball-7/betting/usa-9/wnba-402/player-props/threes'
                },
                'name': 'BetMGM',
                'selectors': {
                    'prop_containers': ['.option', '.market-option', '[data-testid*="option"]', '.ms-option'],
                    'player_names': ['.participant-name', '.player', '.selection-name', '.ms-option-name'],
                    'prop_lines': ['.handicap', '.line-value', '.selection-handicap', '.ms-option-handicap'],
                    'odds': ['.option-odds', '.odds', '.price', '.ms-option-price']
                }
            },
            'caesars': {
                'base_url': 'https://www.caesars.com/sportsbook/basketball/wnba',
                'prop_urls': {
                    'points': 'https://www.caesars.com/sportsbook/basketball/wnba/player-props/points',
                    'rebounds': 'https://www.caesars.com/sportsbook/basketball/wnba/player-props/rebounds',
                    'assists': 'https://www.caesars.com/sportsbook/basketball/wnba/player-props/assists',
                    'threes': 'https://www.caesars.com/sportsbook/basketball/wnba/player-props/threes'
                },
                'name': 'Caesars',
                'selectors': {
                    'prop_containers': ['.selection', '.market-outcome', '[data-cy*="outcome"]', '.czr-outcome'],
                    'player_names': ['.selection-name', '.participant', '.player-name', '.czr-participant'],
                    'prop_lines': ['.selection-handicap', '.line', '.handicap-value'],
                    'odds': ['.selection-odds', '.odds-value', '.price']
                }
            },
            'bet365': {
                'base_url': 'https://www.nc.bet365.com/#/AC/B18/C21056935/D43/E181449/F43/N0/',
                'prop_urls': {
                    'points': 'https://www.nc.bet365.com/#/AC/B18/C21056935/D43/E181449/F43/N0/',
                    'rebounds': 'https://www.nc.bet365.com/#/AC/B18/C21056935/D43/E181447/F43/N0/',
                    'assists': 'https://www.nc.bet365.com/#/AC/B18/C21056935/D43/E181446/F43/N0/',
                    'threes': 'https://www.nc.bet365.com/#/AC/B18/C21056935/D43/E181378/F43/N0/',
                    'combo': 'https://www.nc.bet365.com/#/AC/B18/C21056935/D43/E181448/F43/N0/'
                },
                'name': 'bet365',
                'selectors': {
                    'prop_containers': ['.gl-Participant', '.gl-Market', '.gl-ParticipantOddsOnly', '.gl-ParticipantCentered'],
                    'player_names': ['.gl-ParticipantOddsOnly_Name', '.gl-ParticipantCentered_Name', '.participant-name', '.selection-name'],
                    'prop_lines': ['.gl-ParticipantOddsOnly_Handicap', '.gl-ParticipantCentered_Handicap', '.handicap', '.line-value'],
                    'odds': ['.gl-ParticipantOddsOnly_Odds', '.gl-ParticipantCentered_Odds', '.odds', '.price']
                }
            },
            'espnbet': {
                'url': 'https://espnbet.com/sport/basketball/organization/wnba',
                'name': 'ESPN BET',
                'selectors': {
                    'prop_containers': ['[data-testid*="market"]', '.bet-option', '.market-container'],
                    'player_names': ['.player-name', '.participant-name', '.selection-name'],
                    'prop_lines': ['.line-value', '.handicap', '.prop-line'],
                    'odds': ['.odds-value', '.price', '.bet-odds']
                }
            },
            'pointsbet': {
                'url': 'https://pointsbet.com/basketball/wnba',
                'name': 'PointsBet',
                'selectors': {
                    'prop_containers': ['.market-group', '.outcome-button', '.betting-market'],
                    'player_names': ['.outcome-name', '.participant-name', '.player'],
                    'prop_lines': ['.outcome-handicap', '.line', '.handicap-value'],
                    'odds': ['.outcome-odds', '.odds', '.price']
                }
            },
            'barstool': {
                'url': 'https://www.barstoolsportsbook.com/basketball/wnba',
                'name': 'Barstool',
                'selectors': {
                    'prop_containers': ['.outcome', '.market-outcome', '.bet-option'],
                    'player_names': ['.outcome-name', '.participant', '.player-name'],
                    'prop_lines': ['.outcome-line', '.handicap', '.line-value'],
                    'odds': ['.outcome-odds', '.odds-value', '.price']
                }
            },
            'wynnbet': {
                'url': 'https://www.wynnbet.com/basketball/wnba',
                'name': 'WynnBET',
                'selectors': {
                    'prop_containers': ['.selection', '.outcome', '.market-option'],
                    'player_names': ['.selection-name', '.participant-name', '.player'],
                    'prop_lines': ['.selection-handicap', '.line', '.handicap-value'],
                    'odds': ['.selection-odds', '.odds', '.price']
                }
            },
            'unibet': {
                'url': 'https://www.unibet.com/betting/sports/filter/basketball/wnba',
                'name': 'Unibet',
                'selectors': {
                    'prop_containers': ['.outcome', '.market-outcome', '.bet-button'],
                    'player_names': ['.outcome-label', '.participant-name', '.selection-name'],
                    'prop_lines': ['.outcome-odds-handicap', '.line', '.handicap'],
                    'odds': ['.outcome-odds-value', '.odds', '.price']
                }
            }
        }
        
        # Comprehensive 2025 WNBA player list (all 13 teams)
        self.wnba_players = [
            # Las Vegas Aces
            "A'ja Wilson", "Kelsey Plum", "Jackie Young", "Chelsea Gray", "Kiah Stokes",
            "Alysha Clark", "Tiffany Hayes", "Megan Gustafson", "Kate Martin",

            # New York Liberty
            "Breanna Stewart", "Sabrina Ionescu", "Jonquel Jones", "Betnijah Laney-Hamilton",
            "Courtney Vandersloot", "Leonie Fiebich", "Nyara Sabally", "Kayla Thornton",

            # Minnesota Lynx
            "Napheesa Collier", "Kayla McBride", "Courtney Williams", "Alanna Smith",
            "Bridget Carleton", "Cecilia Zandalasini", "Myisha Hines-Allen",

            # Connecticut Sun
            "Alyssa Thomas", "DeWanna Bonner", "DiJonai Carrington", "Tyasha Harris",
            "Olivia Nelson-Ododa", "Rachel Banham", "Veronica Burton",

            # Seattle Storm
            "Jewell Loyd", "Nneka Ogwumike", "Skylar Diggins-Smith", "Ezi Magbegor",
            "Jordan Horston", "Mercedes Russell", "Gabby Williams",

            # Indiana Fever
            "Caitlin Clark", "Kelsey Mitchell", "Aliyah Boston", "NaLyssa Smith",
            "Lexie Hull", "Katie Lou Samuelson", "Erica Wheeler",

            # Phoenix Mercury
            "Diana Taurasi", "Kahleah Copper", "Natasha Cloud", "Rebecca Allen",
            "Sophie Cunningham", "Mikiah Herbert Harrigan", "Celeste Taylor",

            # Chicago Sky
            "Angel Reese", "Chennedy Carter", "Kamilla Cardoso", "Lindsay Allen",
            "Marina Mabrey", "Michaela Onyenwere", "Dana Evans",

            # Atlanta Dream
            "Rhyne Howard", "Allisha Gray", "Tina Charles", "Nia Coffey",
            "Jordin Canada", "Hazel Renard", "Laeticia Amihere",

            # Dallas Wings
            "Arike Ogunbowale", "Satou Sabally", "Teaira McCowan", "Natasha Howard",
            "Maddy Siegrist", "Sevgi Uzun", "Jacy Sheldon",

            # Washington Mystics
            "Ariel Atkins", "Stefanie Dolson", "Aaliyah Edwards", "Julie Vanloo",
            "Shatori Walker-Kimbrough", "Myisha Hines-Allen", "Jade Melbourne",

            # Golden State Valkyries (New 2025)
            "Kate Martin", "Temi Fagbenle", "Kayla Thornton", "Cecilia Zandalasini",
            "Blake Dietrick", "Iliana Rupert", "Air Hearn",

            # Los Angeles Sparks
            "Dearica Hamby", "Kia Vaughn", "Lexie Brown", "Layshia Clarendon",
            "Azura Stevens", "Li Yueru", "Rickea Jackson"
        ]
        
        # Comprehensive prop types available on sportsbooks
        self.prop_types = [
            # Basic stats
            'points', 'rebounds', 'assists', 'steals', 'blocks', 'turnovers',

            # Shooting props
            'threes', '3-pointers', 'three-pointers', 'field goals', 'free throws',
            'field goal attempts', 'three point attempts', 'free throw attempts',

            # Combo props
            'double-double', 'triple-double', 'pts+reb+ast', 'points+rebounds',
            'points+assists', 'rebounds+assists', 'pts+reb', 'pts+ast', 'reb+ast',

            # Advanced props
            'minutes', 'fantasy points', 'player performance index', 'efficiency',

            # Head-to-head props
            'h2h points', 'h2h rebounds', 'h2h assists', 'h2h threes',

            # Alternative lines
            'alt points', 'alt rebounds', 'alt assists', 'alt threes'
        ]
        
        # Test and filter working proxies
        self.working_proxies = []
        logger.info("🎯 Expert Player Props Scraper initialized with anti-detection")

    def _configure_premium_proxies(self) -> List[Dict]:
        """Configure premium proxy services - Add your credentials here"""
        proxies = []

        # 🏆 RECOMMENDED PREMIUM SERVICES (uncomment and add credentials)

        # 1. SMARTPROXY (Best for sports betting sites)
        # SMARTPROXY_USER = "your_username"
        # SMARTPROXY_PASS = "your_password"
        # proxies.extend([
        #     {'http': f'http://{SMARTPROXY_USER}:{SMARTPROXY_PASS}@gate.smartproxy.com:7000',
        #      'https': f'https://{SMARTPROXY_USER}:{SMARTPROXY_PASS}@gate.smartproxy.com:7000'},
        #     {'http': f'http://{SMARTPROXY_USER}:{SMARTPROXY_PASS}@gate.smartproxy.com:7001',
        #      'https': f'https://{SMARTPROXY_USER}:{SMARTPROXY_PASS}@gate.smartproxy.com:7001'},
        # ])

        # 2. OXYLABS (Premium datacenter + residential)
        # OXYLABS_USER = "your_username"
        # OXYLABS_PASS = "your_password"
        # proxies.extend([
        #     {'http': f'http://{OXYLABS_USER}:{OXYLABS_PASS}@pr.oxylabs.io:7777',
        #      'https': f'https://{OXYLABS_USER}:{OXYLABS_PASS}@pr.oxylabs.io:7777'},
        #     {'http': f'http://{OXYLABS_USER}:{OXYLABS_PASS}@dc.oxylabs.io:8001',
        #      'https': f'https://{OXYLABS_USER}:{OXYLABS_PASS}@dc.oxylabs.io:8001'},
        # ])

        # 3. BRIGHT DATA (Formerly Luminati - highest success rate)
        # BRIGHT_USER = "your_username"
        # BRIGHT_PASS = "your_password"
        # BRIGHT_ZONE = "your_zone"
        # proxies.extend([
        #     {'http': f'http://{BRIGHT_USER}-zone-{BRIGHT_ZONE}:{BRIGHT_PASS}@zproxy.lum-superproxy.io:22225',
        #      'https': f'https://{BRIGHT_USER}-zone-{BRIGHT_ZONE}:{BRIGHT_PASS}@zproxy.lum-superproxy.io:22225'},
        # ])

        # 4. PROXYMESH (Rotating proxies)
        # PROXYMESH_USER = "your_username"
        # PROXYMESH_PASS = "your_password"
        # proxies.extend([
        #     {'http': f'http://{PROXYMESH_USER}:{PROXYMESH_PASS}@rotating-residential.proxymesh.com:31280',
        #      'https': f'https://{PROXYMESH_USER}:{PROXYMESH_PASS}@rotating-residential.proxymesh.com:31280'},
        # ])

        # 5. STORM PROXIES (Budget option)
        # STORM_USER = "your_username"
        # STORM_PASS = "your_password"
        # proxies.extend([
        #     {'http': f'http://{STORM_USER}:{STORM_PASS}@rotating-residential.stormproxies.cn:9000',
        #      'https': f'https://{STORM_USER}:{STORM_PASS}@rotating-residential.stormproxies.cn:9000'},
        # ])

        # FALLBACK: Free proxies (unreliable but better than nothing)
        if not proxies:
            logger.warning("⚠️ No premium proxies configured - using free proxies (unreliable)")
            proxies = [
                {'http': 'http://103.152.112.162:80', 'https': 'https://103.152.112.162:80'},
                {'http': 'http://***************:7492', 'https': 'https://***************:7492'},
                {'http': 'http://*************:80', 'https': 'https://*************:80'},
            ]

        logger.info(f"🔄 Configured {len(proxies)} proxy endpoints")
        return proxies

    async def test_proxies(self) -> List[Dict]:
        """Test proxy connectivity and filter working ones"""
        logger.info("🔍 Testing proxy connectivity...")
        working_proxies = []

        test_url = "http://httpbin.org/ip"  # Simple IP check service

        for i, proxy in enumerate(self.proxies):
            try:
                logger.info(f"🧪 Testing proxy {i+1}/{len(self.proxies)}...")

                response = requests.get(
                    test_url,
                    proxies=proxy,
                    timeout=10,
                    headers={'User-Agent': random.choice(self.user_agents)}
                )

                if response.status_code == 200:
                    ip_info = response.json()
                    logger.info(f"✅ Proxy {i+1} working - IP: {ip_info.get('origin', 'Unknown')}")
                    working_proxies.append(proxy)
                else:
                    logger.warning(f"⚠️ Proxy {i+1} returned status {response.status_code}")

            except Exception as e:
                logger.warning(f"❌ Proxy {i+1} failed: {str(e)[:50]}...")
                continue

        self.working_proxies = working_proxies
        logger.info(f"🎯 Proxy test complete: {len(working_proxies)}/{len(self.proxies)} working")

        if working_proxies:
            self.proxy_cycle = cycle(working_proxies)

        return working_proxies

    def _create_driver(self) -> Optional[webdriver.Chrome]:
        """Create expert stealth Chrome driver with advanced anti-detection"""
        if not SELENIUM_AVAILABLE:
            logger.error("❌ Selenium not available")
            return None

        try:
            logger.info("🚗 Creating expert stealth Chrome driver...")

            options = Options()

            # Basic stealth options
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-software-rasterizer')
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--disable-features=TranslateUI')
            options.add_argument('--disable-ipc-flooding-protection')
            options.add_argument('--disable-web-security')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--enable-unsafe-swiftshader')

            # Advanced anti-detection
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-images')  # Faster loading
            options.add_argument('--no-first-run')
            options.add_argument('--no-default-browser-check')
            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-sync')
            options.add_argument('--disable-translate')
            options.add_argument('--hide-scrollbars')
            options.add_argument('--mute-audio')

            # Random window size for human-like behavior
            window_sizes = ['1920,1080', '1366,768', '1440,900', '1536,864', '1280,720', '1600,900']
            options.add_argument(f'--window-size={random.choice(window_sizes)}')

            # Rotate user agents for human-like behavior
            user_agent = random.choice(self.user_agents)
            options.add_argument(f'--user-agent={user_agent}')
            logger.info(f"🎭 Using user agent: {user_agent[:50]}...")

            # Advanced experimental options
            options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
            options.add_experimental_option('useAutomationExtension', False)
            options.add_experimental_option("detach", True)

            # Add proxy if available
            if self.proxy_cycle:
                try:
                    proxy = next(self.proxy_cycle)
                    proxy_server = proxy['http'].replace('http://', '')
                    options.add_argument(f'--proxy-server={proxy_server}')
                    logger.info(f"🔄 Using proxy: {proxy_server}")
                except:
                    logger.warning("⚠️ Proxy configuration failed, continuing without proxy")

            # Suppress all logging
            options.add_argument('--log-level=3')
            options.add_argument('--silent')
            options.add_argument('--disable-logging')

            # Performance and stealth optimizations
            prefs = {
                "profile.default_content_setting_values": {
                    "images": 2,  # Block images for faster loading
                    "plugins": 2,  # Block plugins
                    "popups": 2,  # Block popups
                    "geolocation": 2,  # Block location sharing
                    "notifications": 2,  # Block notifications
                    "media_stream": 2,  # Block media stream
                },
                "profile.managed_default_content_settings": {
                    "images": 2
                }
            }
            options.add_experimental_option("prefs", prefs)

            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)

            # Advanced anti-detection scripts
            stealth_scripts = [
                "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})",
                "Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})",
                "Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})",
                "window.chrome = { runtime: {} }",
                "Object.defineProperty(navigator, 'permissions', {get: () => ({query: () => Promise.resolve({state: 'granted'})})})",
                "Object.defineProperty(navigator, 'platform', {get: () => 'Win32'})",
                "Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 4})"
            ]

            for script in stealth_scripts:
                try:
                    driver.execute_script(script)
                except Exception as e:
                    logger.debug(f"Stealth script failed: {e}")

            # Set random viewport and screen properties
            viewport_script = f"""
            Object.defineProperty(screen, 'width', {{get: () => {random.randint(1200, 1920)}}});
            Object.defineProperty(screen, 'height', {{get: () => {random.randint(800, 1200)}}});
            Object.defineProperty(screen, 'availWidth', {{get: () => screen.width}});
            Object.defineProperty(screen, 'availHeight', {{get: () => screen.height - 40}});
            """
            driver.execute_script(viewport_script)

            # Human-like mouse movements simulation
            driver.execute_script("""
                document.addEventListener('mousemove', function(e) {
                    window.lastMouseMove = Date.now();
                });
            """)

            logger.info("✅ Expert stealth Chrome driver created with advanced anti-detection")
            return driver

        except Exception as e:
            logger.error(f"❌ Expert driver creation failed: {e}")
            return None

    def _create_firefox_driver(self) -> Optional[webdriver.Firefox]:
        """Create Firefox driver as alternative when Chrome is blocked"""
        try:
            from selenium.webdriver.firefox.service import Service as FirefoxService
            from selenium.webdriver.firefox.options import Options as FirefoxOptions
            from webdriver_manager.firefox import GeckoDriverManager

            logger.info("🦊 Creating Firefox driver as alternative...")

            options = FirefoxOptions()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')

            # Firefox-specific stealth options
            options.set_preference("general.useragent.override", random.choice(self.user_agents))
            options.set_preference("dom.webdriver.enabled", False)
            options.set_preference("useAutomationExtension", False)
            options.set_preference("marionette.enabled", True)

            service = FirefoxService(GeckoDriverManager().install())
            driver = webdriver.Firefox(service=service, options=options)

            # Firefox stealth scripts
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            logger.info("✅ Firefox driver created successfully")
            return driver

        except Exception as e:
            logger.debug(f"Firefox driver creation failed: {e}")
            return None

    async def _human_like_delay(self, delay_type: str = 'between_actions'):
        """Add human-like delays to avoid detection"""
        min_delay, max_delay = self.human_delays.get(delay_type, (1, 3))
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)

    def _simulate_human_behavior(self, driver):
        """Simulate human-like behavior on the page"""
        try:
            # Random scroll to simulate reading
            scroll_script = f"""
                window.scrollTo(0, {random.randint(100, 500)});
                setTimeout(() => window.scrollTo(0, {random.randint(200, 800)}), {random.randint(500, 1500)});
            """
            driver.execute_script(scroll_script)

            # Simulate mouse movement
            mouse_script = """
                var event = new MouseEvent('mousemove', {
                    'view': window,
                    'bubbles': true,
                    'cancelable': true,
                    'clientX': Math.random() * window.innerWidth,
                    'clientY': Math.random() * window.innerHeight
                });
                document.dispatchEvent(event);
            """
            driver.execute_script(mouse_script)

        except Exception as e:
            logger.debug(f"Human behavior simulation error: {e}")

    def _check_for_captcha_or_blocks(self, driver) -> bool:
        """Enhanced check for blocks, captchas, and anti-bot measures"""
        try:
            page_source = driver.page_source.lower()
            current_url = driver.current_url.lower()
            page_title = driver.title.lower()

            # Enhanced blocking indicators
            block_indicators = [
                'captcha', 'recaptcha', 'blocked', 'access denied',
                'rate limit', 'too many requests', 'suspicious activity',
                'verify you are human', 'cloudflare', 'bot detection',
                'security check', 'unusual traffic', 'automated requests',
                'please wait', 'checking your browser', 'ddos protection',
                'access to this page has been denied', 'forbidden',
                'your request has been blocked', 'anti-bot'
            ]

            # Check page source
            for indicator in block_indicators:
                if indicator in page_source:
                    logger.warning(f"🚫 Detected blocking in source: {indicator}")
                    return True

            # Check URL for redirects
            block_urls = ['block', 'captcha', 'denied', 'forbidden', 'security']
            for block_url in block_urls:
                if block_url in current_url:
                    logger.warning(f"🚫 Redirected to blocking page: {current_url}")
                    return True

            # Check page title
            if any(indicator in page_title for indicator in ['blocked', 'denied', 'captcha']):
                logger.warning(f"🚫 Blocking detected in title: {page_title}")
                return True

            # Check for empty or minimal content (common with blocks)
            if len(page_source) < 1000:
                logger.warning(f"🚫 Suspiciously small page content: {len(page_source)} chars")
                return True

            # Check for specific DraftKings blocking patterns
            if 'draftkings' in current_url and 'wnba' not in page_source and 'basketball' not in page_source:
                logger.warning("🚫 DraftKings page missing expected WNBA content")
                return True

            return False

        except Exception as e:
            logger.debug(f"Block detection error: {e}")
            return False

    async def _handle_blocking_with_alternatives(self, driver, sportsbook_name: str) -> bool:
        """Handle blocking by trying alternative approaches"""
        try:
            logger.info(f"🔄 Attempting to bypass {sportsbook_name} blocking...")

            # Strategy 1: Clear cookies and reload
            driver.delete_all_cookies()
            await self._human_like_delay('page_load')

            # Strategy 2: Try mobile user agent
            mobile_ua = "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"
            driver.execute_script(f"Object.defineProperty(navigator, 'userAgent', {{get: () => '{mobile_ua}'}});")

            # Strategy 3: Simulate coming from Google
            driver.execute_script("Object.defineProperty(document, 'referrer', {get: () => 'https://www.google.com/'});")

            # Strategy 4: Add realistic browser history
            driver.execute_script("""
                window.history.pushState({}, '', window.location.href);
                window.history.pushState({}, '', 'https://www.google.com/search?q=wnba+player+props');
                window.history.back();
            """)

            await self._human_like_delay('between_actions')
            return True

        except Exception as e:
            logger.debug(f"Alternative blocking bypass failed: {e}")
            return False

    async def _try_alternative_urls(self, driver, base_urls: list) -> bool:
        """Try alternative URLs when main ones are blocked"""
        for alt_url in base_urls:
            try:
                logger.info(f"🔄 Trying alternative URL: {alt_url}")
                driver.get(alt_url)
                await self._human_like_delay('page_load')

                if not self._check_for_captcha_or_blocks(driver):
                    logger.info("✅ Alternative URL successful")
                    return True

            except Exception as e:
                logger.debug(f"Alternative URL failed: {e}")
                continue

        return False

    async def _scrape_with_requests_fallback(self, url: str, sportsbook_name: str) -> List[PlayerProp]:
        """Fallback scraping using requests when Selenium is blocked"""
        props = []

        try:
            logger.info(f"🌐 Trying requests fallback for {sportsbook_name}...")

            # Create session with realistic headers
            session = requests.Session()

            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0',
                'Referer': 'https://www.google.com/'
            }

            session.headers.update(headers)

            # Add proxy if available
            proxies = None
            if self.proxy_cycle:
                try:
                    proxies = next(self.proxy_cycle)
                except:
                    pass

            # Make request with timeout
            response = session.get(url, timeout=15, proxies=proxies)

            if response.status_code == 200:
                # Basic text extraction from HTML
                html_content = response.text.lower()

                # Look for player names and prop data in the HTML
                for player in self.wnba_players:
                    if player.lower() in html_content:
                        # Try to extract props around player names
                        # This is a simplified extraction - would need more sophisticated parsing
                        logger.info(f"📊 Found {player} in {sportsbook_name} HTML")

                logger.info(f"✅ Requests fallback successful for {sportsbook_name}")
            else:
                logger.warning(f"⚠️ Requests fallback got status {response.status_code}")

        except Exception as e:
            logger.debug(f"Requests fallback error: {e}")

        return props

    async def scrape_draftkings_props(self) -> List[PlayerProp]:
        """Expert DraftKings WNBA player props scraper using specific prop URLs"""
        props = []
        driver = None

        try:
            logger.info("🎯 Expert DraftKings WNBA props scraping...")

            # Try Chrome first, then Firefox if blocked
            driver = self._create_driver()
            if not driver:
                logger.warning("⚠️ Chrome driver failed, trying Firefox...")
                driver = self._create_firefox_driver()
                if not driver:
                    logger.error("❌ Both Chrome and Firefox drivers failed")
                    return props

            dk_config = self.sportsbooks['draftkings']

            # Scrape each prop category separately for better results
            for prop_category, prop_url in dk_config['prop_urls'].items():
                try:
                    logger.info(f"🔍 Scraping {prop_category} props from DraftKings...")

                    # Navigate to specific prop URL with human-like behavior
                    driver.get(prop_url)
                    await self._human_like_delay('page_load')

                    # Enhanced blocking detection and handling
                    if self._check_for_captcha_or_blocks(driver):
                        logger.warning(f"🚫 Blocked on {prop_category}, trying alternatives...")

                        # Try alternative approaches
                        if await self._handle_blocking_with_alternatives(driver, 'DraftKings'):
                            # Retry the same URL
                            driver.get(prop_url)
                            await self._human_like_delay('page_load')

                            if self._check_for_captcha_or_blocks(driver):
                                # Try alternative DraftKings URLs
                                alt_urls = [
                                    'https://sportsbook.draftkings.com/leagues/basketball/88670846',
                                    'https://sportsbook.draftkings.com/sports/basketball/88670846',
                                    'https://sportsbook.draftkings.com/featured/wnba',
                                    dk_config['base_url']
                                ]

                                if not await self._try_alternative_urls(driver, alt_urls):
                                    logger.error(f"❌ All DraftKings alternatives failed for {prop_category}")
                                    continue
                            else:
                                logger.info(f"✅ Successfully bypassed blocking for {prop_category}")
                        else:
                            logger.warning(f"⚠️ Selenium blocked for {prop_category}, trying requests fallback...")
                            fallback_props = await self._scrape_with_requests_fallback(prop_url, 'DraftKings')
                            props.extend(fallback_props)
                            continue

                    # Simulate human behavior
                    self._simulate_human_behavior(driver)

                    # Wait for page to load completely with multiple strategies
                    try:
                        WebDriverWait(driver, 15).until(
                            EC.any_of(
                                EC.presence_of_element_located((By.CSS_SELECTOR, ".sportsbook-table")),
                                EC.presence_of_element_located((By.CSS_SELECTOR, ".sportsbook-outcome")),
                                EC.presence_of_element_located((By.CSS_SELECTOR, "body"))
                            )
                        )
                    except TimeoutException:
                        logger.warning(f"⚠️ Timeout loading DraftKings {prop_category} page")
                        continue

                    # Check if we're on the right page
                    page_source = driver.page_source.lower()
                    if 'wnba' not in page_source:
                        logger.warning(f"⚠️ No WNBA content on {prop_category} page")
                        continue

                    # Extract props using multiple methods
                    category_props = await self._extract_draftkings_props_expert(driver, prop_category)
                    props.extend(category_props)

                    logger.info(f"✅ {prop_category}: Found {len(category_props)} props")

                    # Rate limiting between categories
                    await asyncio.sleep(random.uniform(2, 4))

                except Exception as e:
                    logger.error(f"❌ Error scraping {prop_category}: {e}")
                    continue

            logger.info(f"🎯 DraftKings total: {len(props)} player props")

        except Exception as e:
            logger.error(f"❌ DraftKings scraping error: {e}")

        finally:
            if driver:
                driver.quit()

        return props

    async def _extract_draftkings_props_expert(self, driver, prop_category: str) -> List[PlayerProp]:
        """Expert extraction method for DraftKings props with multiple strategies"""
        props = []

        try:
            # Strategy 1: Look for sportsbook table rows
            table_rows = driver.find_elements(By.CSS_SELECTOR,
                '.sportsbook-table__body-row, .sportsbook-table-row, tr[data-testid*="outcome"]')

            for row in table_rows[:50]:
                try:
                    prop_data = self._parse_draftkings_row(row, prop_category)
                    if prop_data:
                        props.append(prop_data)
                except:
                    continue

            # Strategy 2: Look for outcome cells
            if len(props) < 5:  # If we didn't find many props, try another method
                outcome_cells = driver.find_elements(By.CSS_SELECTOR,
                    '.sportsbook-outcome-cell, .outcome, [data-testid*="outcome"]')

                for cell in outcome_cells[:100]:
                    try:
                        prop_data = self._parse_draftkings_cell(cell, prop_category)
                        if prop_data:
                            props.append(prop_data)
                    except:
                        continue

            # Strategy 3: Text-based extraction as fallback
            if len(props) < 3:
                all_elements = driver.find_elements(By.CSS_SELECTOR, 'div, span, button, a')
                for element in all_elements[:200]:
                    try:
                        text = element.text.strip()
                        if len(text) > 10:
                            prop_data = self._extract_prop_from_text_expert(text, 'DraftKings', prop_category)
                            if prop_data:
                                props.append(prop_data)
                    except:
                        continue

            # Remove duplicates
            unique_props = []
            seen = set()
            for prop in props:
                key = f"{prop.player_name}_{prop.prop_type}_{prop.line}"
                if key not in seen:
                    seen.add(key)
                    unique_props.append(prop)

            return unique_props

        except Exception as e:
            logger.debug(f"Expert extraction error: {e}")
            return props

    def _parse_draftkings_row(self, row_element, prop_category: str) -> Optional[PlayerProp]:
        """Parse a DraftKings table row for prop data"""
        try:
            row_text = row_element.text.strip()

            # Look for player name in the row
            player_name = None
            for player in self.wnba_players:
                if player.lower() in row_text.lower():
                    player_name = player
                    break

            if not player_name:
                return None

            # Extract line and odds from row
            line_match = re.search(r'(\d+\.?\d*)', row_text)
            if not line_match:
                return None

            line = float(line_match.group(1))

            # Extract odds
            odds_matches = re.findall(r'[+-]\d{3,4}', row_text)
            over_odds = int(odds_matches[0]) if odds_matches else -110
            under_odds = int(odds_matches[1]) if len(odds_matches) > 1 else -110

            return PlayerProp(
                player_name=player_name,
                prop_type=prop_category,
                line=line,
                over_odds=over_odds,
                under_odds=under_odds,
                sportsbook='DraftKings',
                game_info=f"WNBA {prop_category}",
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.debug(f"Row parsing error: {e}")
            return None

    def _parse_draftkings_cell(self, cell_element, prop_category: str) -> Optional[PlayerProp]:
        """Parse a DraftKings outcome cell for prop data"""
        try:
            cell_text = cell_element.text.strip()

            # Look for player name
            player_name = None
            for player in self.wnba_players:
                if player.lower() in cell_text.lower():
                    player_name = player
                    break

            if not player_name:
                return None

            # Extract line and odds
            line_match = re.search(r'(?:over|under|o|u)\s*(\d+\.?\d*)', cell_text.lower())
            if not line_match:
                line_match = re.search(r'(\d+\.?\d*)', cell_text)

            if not line_match:
                return None

            line = float(line_match.group(1))

            # Extract odds
            odds_match = re.search(r'([+-]\d{3,4})', cell_text)
            odds = int(odds_match.group(1)) if odds_match else -110

            # Determine if it's over or under
            is_over = 'over' in cell_text.lower() or 'o' in cell_text.lower()

            return PlayerProp(
                player_name=player_name,
                prop_type=prop_category,
                line=line,
                over_odds=odds if is_over else -110,
                under_odds=odds if not is_over else -110,
                sportsbook='DraftKings',
                game_info=f"WNBA {prop_category}",
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.debug(f"Cell parsing error: {e}")
            return None

    async def scrape_fanduel_props(self) -> List[PlayerProp]:
        """Expert FanDuel WNBA player props scraper using specific prop URLs"""
        props = []
        driver = None

        try:
            logger.info("🎯 Expert FanDuel WNBA props scraping...")

            driver = self._create_driver()
            if not driver:
                return props

            fd_config = self.sportsbooks['fanduel']

            # Scrape each prop category
            for prop_category, prop_url in fd_config['prop_urls'].items():
                try:
                    logger.info(f"🔍 Scraping {prop_category} props from FanDuel...")

                    # Navigate to specific prop URL with expert behavior
                    driver.get(prop_url)
                    await self._human_like_delay('page_load')

                    # Check for blocks/captcha
                    if self._check_for_captcha_or_blocks(driver):
                        logger.warning(f"🚫 FanDuel blocked on {prop_category}, skipping...")
                        continue

                    # Simulate human behavior
                    self._simulate_human_behavior(driver)

                    # Wait for dynamic content to load with multiple strategies
                    try:
                        WebDriverWait(driver, 15).until(
                            EC.any_of(
                                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-test-id*='Market']")),
                                EC.presence_of_element_located((By.CSS_SELECTOR, ".outcome-cell")),
                                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-test-id]"))
                            )
                        )
                    except TimeoutException:
                        logger.warning(f"⚠️ Timeout loading FanDuel {prop_category} page")
                        continue

                    # Extract props using FanDuel-specific methods
                    category_props = await self._extract_fanduel_props_expert(driver, prop_category)
                    props.extend(category_props)

                    logger.info(f"✅ {prop_category}: Found {len(category_props)} props")

                    # Rate limiting
                    await asyncio.sleep(random.uniform(2, 4))

                except Exception as e:
                    logger.error(f"❌ Error scraping FanDuel {prop_category}: {e}")
                    continue

            logger.info(f"🎯 FanDuel total: {len(props)} player props")

        except Exception as e:
            logger.error(f"❌ FanDuel scraping error: {e}")

        finally:
            if driver:
                driver.quit()

        return props

    async def _extract_fanduel_props_expert(self, driver, prop_category: str) -> List[PlayerProp]:
        """Expert extraction method for FanDuel props"""
        props = []

        try:
            # Strategy 1: Look for FanDuel outcome cells
            outcome_cells = driver.find_elements(By.CSS_SELECTOR,
                '[data-test-id*="outcome-cell"], .outcome-cell, [data-test-id*="Market"]')

            for cell in outcome_cells[:50]:
                try:
                    prop_data = self._parse_fanduel_cell(cell, prop_category)
                    if prop_data:
                        props.append(prop_data)
                except:
                    continue

            # Strategy 2: Look for market containers
            if len(props) < 5:
                market_containers = driver.find_elements(By.CSS_SELECTOR,
                    '.market-container, [data-test-id*="market"], .prop-market')

                for container in market_containers[:30]:
                    try:
                        container_props = self._parse_fanduel_market(container, prop_category)
                        props.extend(container_props)
                    except:
                        continue

            # Strategy 3: Generic text extraction
            if len(props) < 3:
                all_elements = driver.find_elements(By.CSS_SELECTOR, 'button, div, span')
                for element in all_elements[:150]:
                    try:
                        text = element.text.strip()
                        if len(text) > 10:
                            prop_data = self._extract_prop_from_text_expert(text, 'FanDuel', prop_category)
                            if prop_data:
                                props.append(prop_data)
                    except:
                        continue

            # Remove duplicates
            unique_props = []
            seen = set()
            for prop in props:
                key = f"{prop.player_name}_{prop.prop_type}_{prop.line}"
                if key not in seen:
                    seen.add(key)
                    unique_props.append(prop)

            return unique_props

        except Exception as e:
            logger.debug(f"FanDuel expert extraction error: {e}")
            return props

    def _parse_fanduel_cell(self, cell_element, prop_category: str) -> Optional[PlayerProp]:
        """Parse a FanDuel outcome cell for prop data"""
        try:
            cell_text = cell_element.text.strip()

            # Look for player name
            player_name = None
            for player in self.wnba_players:
                if player.lower() in cell_text.lower():
                    player_name = player
                    break

            if not player_name:
                return None

            # Extract line and odds
            line_match = re.search(r'(?:over|under|o|u)\s*(\d+\.?\d*)', cell_text.lower())
            if not line_match:
                line_match = re.search(r'(\d+\.?\d*)', cell_text)

            if not line_match:
                return None

            line = float(line_match.group(1))

            # Extract odds
            odds_match = re.search(r'([+-]\d{3,4})', cell_text)
            odds = int(odds_match.group(1)) if odds_match else -110

            return PlayerProp(
                player_name=player_name,
                prop_type=prop_category,
                line=line,
                over_odds=odds,
                under_odds=-110,
                sportsbook='FanDuel',
                game_info=f"WNBA {prop_category}",
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.debug(f"FanDuel cell parsing error: {e}")
            return None

    def _parse_fanduel_market(self, market_element, prop_category: str) -> List[PlayerProp]:
        """Parse a FanDuel market container for multiple props"""
        props = []

        try:
            market_text = market_element.text.strip()

            # Split by lines and look for individual props
            lines = market_text.split('\n')

            for line in lines:
                if len(line.strip()) > 10:
                    prop_data = self._extract_prop_from_text_expert(line.strip(), 'FanDuel', prop_category)
                    if prop_data:
                        props.append(prop_data)

            return props

        except Exception as e:
            logger.debug(f"FanDuel market parsing error: {e}")
            return props

    async def scrape_generic_sportsbook(self, sportsbook_id: str) -> List[PlayerProp]:
        """Generic scraper that works for multiple sportsbooks"""
        props = []
        driver = None

        if sportsbook_id not in self.sportsbooks:
            logger.warning(f"⚠️ Unknown sportsbook: {sportsbook_id}")
            return props

        sportsbook_config = self.sportsbooks[sportsbook_id]

        try:
            logger.info(f"🎯 Scraping {sportsbook_config['name']} player props...")

            driver = self._create_driver()
            if not driver:
                return props

            # Navigate to sportsbook
            driver.get(sportsbook_config['url'])
            time.sleep(5)

            # Check if page loaded
            page_title = driver.title
            logger.info(f"📄 {sportsbook_config['name']} page title: {page_title}")

            # Look for WNBA content
            page_source = driver.page_source.lower()
            wnba_indicators = ['wnba', 'women', 'basketball', 'fever', 'aces', 'liberty', 'sun']

            if not any(indicator in page_source for indicator in wnba_indicators):
                logger.warning(f"⚠️ No WNBA content detected on {sportsbook_config['name']}")

            # Try multiple approaches to find props
            approaches = [
                self._scrape_with_selectors,
                self._scrape_with_text_search,
                self._scrape_with_generic_elements
            ]

            for approach in approaches:
                try:
                    found_props = approach(driver, sportsbook_config)
                    props.extend(found_props)
                    if props:
                        break  # Stop if we found props
                except Exception as e:
                    logger.debug(f"Approach failed: {e}")
                    continue

            logger.info(f"✅ {sportsbook_config['name']}: Found {len(props)} player props")

        except Exception as e:
            logger.error(f"❌ {sportsbook_config['name']} scraping error: {e}")

        finally:
            if driver:
                driver.quit()

        return props

    def _scrape_with_selectors(self, driver, sportsbook_config: Dict) -> List[PlayerProp]:
        """Try scraping using configured selectors"""
        props = []
        selectors = sportsbook_config['selectors']

        # Try each prop container selector
        for container_selector in selectors['prop_containers']:
            try:
                containers = driver.find_elements(By.CSS_SELECTOR, container_selector)
                for container in containers[:50]:
                    text = container.text.strip()
                    prop_data = self._extract_prop_from_text(text, sportsbook_config['name'])
                    if prop_data:
                        props.append(prop_data)
            except:
                continue

        return props

    def _scrape_with_text_search(self, driver, sportsbook_config: Dict) -> List[PlayerProp]:
        """Search for props by looking for player names and prop types in text"""
        props = []

        # Get all text elements
        all_elements = driver.find_elements(By.CSS_SELECTOR, '*')

        for element in all_elements[:300]:
            try:
                text = element.text.strip()
                if len(text) > 5:
                    # Check if text contains player name and prop type
                    has_player = any(player.lower() in text.lower() for player in self.wnba_players)
                    has_prop = any(prop_type in text.lower() for prop_type in self.prop_types)

                    if has_player and has_prop:
                        prop_data = self._extract_prop_from_text(text, sportsbook_config['name'])
                        if prop_data:
                            props.append(prop_data)
            except:
                continue

        return props

    def _scrape_with_generic_elements(self, driver, sportsbook_config: Dict) -> List[PlayerProp]:
        """Generic element scraping as last resort"""
        props = []

        # Look for common betting element types
        element_types = ['button', 'a', 'div[role="button"]', 'span', 'td', 'li']

        for element_type in element_types:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, element_type)

                for element in elements[:100]:
                    try:
                        text = element.text.strip()
                        if len(text) > 10:
                            # Look for patterns like "Player Name Points Over 20.5 -110"
                            if any(keyword in text.lower() for keyword in ['over', 'under', 'points', 'rebounds', 'assists']):
                                prop_data = self._extract_prop_from_text(text, sportsbook_config['name'])
                                if prop_data:
                                    props.append(prop_data)
                    except:
                        continue

                if props:
                    break  # Stop if we found props

            except:
                continue

        return props

    def _extract_prop_from_text_expert(self, text: str, sportsbook: str, prop_category: str = None) -> Optional[PlayerProp]:
        """Expert prop extraction with advanced regex patterns and validation"""
        try:
            # Find player name with fuzzy matching
            player_name = None
            for player in self.wnba_players:
                # Try exact match first
                if player.lower() in text.lower():
                    player_name = player
                    break
                # Try partial match (last name only)
                last_name = player.split()[-1].lower()
                if len(last_name) > 3 and last_name in text.lower():
                    player_name = player
                    break

            if not player_name:
                return None

            # Determine prop type (use category if provided, otherwise detect)
            prop_type = prop_category
            if not prop_type:
                for prop in self.prop_types:
                    if prop in text.lower():
                        prop_type = prop
                        break

            if not prop_type:
                return None

            # Advanced line extraction with multiple patterns
            line = None

            # Pattern 1: "Over/Under X.X"
            line_match = re.search(r'(?:over|under|o|u)\s*(\d+\.?\d*)', text.lower())
            if line_match:
                line = float(line_match.group(1))

            # Pattern 2: "X.X points/rebounds/assists"
            if not line:
                line_match = re.search(r'(\d+\.?\d*)\s*(?:points|rebounds|assists|threes|steals|blocks|pts|reb|ast)', text.lower())
                if line_match:
                    line = float(line_match.group(1))

            # Pattern 3: Just numbers near prop type
            if not line:
                line_match = re.search(r'(\d+\.?\d*)', text)
                if line_match:
                    potential_line = float(line_match.group(1))
                    # Validate line makes sense for prop type
                    if self._validate_prop_line(prop_type, potential_line):
                        line = potential_line

            if not line:
                return None

            # Advanced odds extraction
            odds_matches = re.findall(r'([+-]\d{3,4})', text)

            # Default odds
            over_odds = -110
            under_odds = -110

            if odds_matches:
                if len(odds_matches) == 1:
                    # Single odds - determine if over or under based on context
                    odds_value = int(odds_matches[0])
                    if 'over' in text.lower() or 'o' in text.lower():
                        over_odds = odds_value
                    elif 'under' in text.lower() or 'u' in text.lower():
                        under_odds = odds_value
                    else:
                        over_odds = odds_value
                elif len(odds_matches) >= 2:
                    over_odds = int(odds_matches[0])
                    under_odds = int(odds_matches[1])

            # Extract game info if available
            game_info = "Today's Game"
            team_match = re.search(r'(vs|@|v)\s*([A-Z]{2,4})', text.upper())
            if team_match:
                game_info = f"vs {team_match.group(2)}"

            return PlayerProp(
                player_name=player_name,
                prop_type=prop_type,
                line=line,
                over_odds=over_odds,
                under_odds=under_odds,
                sportsbook=sportsbook,
                game_info=game_info,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.debug(f"Expert prop extraction error: {e}")
            return None

    def _validate_prop_line(self, prop_type: str, line: float) -> bool:
        """Validate if a line makes sense for a given prop type"""
        try:
            if prop_type in ['points', 'pts']:
                return 5.0 <= line <= 40.0
            elif prop_type in ['rebounds', 'reb']:
                return 2.0 <= line <= 20.0
            elif prop_type in ['assists', 'ast']:
                return 1.0 <= line <= 15.0
            elif prop_type in ['threes', '3-pointers', 'three-pointers']:
                return 0.5 <= line <= 8.0
            elif prop_type in ['steals']:
                return 0.5 <= line <= 5.0
            elif prop_type in ['blocks']:
                return 0.5 <= line <= 4.0
            elif prop_type in ['minutes']:
                return 10.0 <= line <= 40.0
            else:
                return True  # Allow other prop types
        except:
            return True

    def _extract_prop_from_text(self, text: str, sportsbook: str) -> Optional[PlayerProp]:
        """Legacy method - redirect to expert version"""
        return self._extract_prop_from_text_expert(text, sportsbook)
    
    async def scrape_all_real_props(self, max_sportsbooks: int = 5) -> List[PlayerProp]:
        """Scrape real props from all available sportsbooks with fallback"""
        logger.info("🕷️ Starting comprehensive player props scraping...")

        all_props = []

        # Expert priority order for sportsbooks (most reliable first)
        priority_sportsbooks = [
            'draftkings',  # Best structured data
            'fanduel',     # Good API-like structure
            'betmgm',      # Reliable selectors
            'bet365',      # Your specific URLs
            'caesars',     # Good for backup
            'espnbet',     # ESPN reliability
            'pointsbet',   # Alternative source
        ]

        scraped_count = 0

        for sportsbook_id in priority_sportsbooks:
            if scraped_count >= max_sportsbooks:
                break

            try:
                logger.info(f"🎯 Scraping {self.sportsbooks[sportsbook_id]['name']}...")

                # Expert scraping with retry logic
                retry_count = 0
                max_retries = 2

                while retry_count <= max_retries:
                    try:
                        # Use specific scrapers for DraftKings and FanDuel, generic for others
                        if sportsbook_id == 'draftkings':
                            props = await self.scrape_draftkings_props()
                        elif sportsbook_id == 'fanduel':
                            props = await self.scrape_fanduel_props()
                        else:
                            props = await self.scrape_generic_sportsbook(sportsbook_id)

                        all_props.extend(props)
                        scraped_count += 1

                        logger.info(f"✅ {self.sportsbooks[sportsbook_id]['name']}: {len(props)} props")
                        break  # Success, exit retry loop

                    except Exception as retry_error:
                        retry_count += 1
                        logger.warning(f"⚠️ Retry {retry_count}/{max_retries} for {sportsbook_id}: {retry_error}")
                        if retry_count <= max_retries:
                            await self._human_like_delay('between_sportsbooks')
                        else:
                            logger.error(f"❌ Failed all retries for {sportsbook_id}")

                # Expert rate limiting between sportsbooks
                await self._human_like_delay('between_sportsbooks')

                # If we found good props, we can be less aggressive
                if len(all_props) > 20:
                    logger.info("🎯 Found sufficient props, reducing scraping intensity")
                    break

            except Exception as e:
                logger.error(f"❌ Error scraping {sportsbook_id}: {e}")
                continue

        logger.info(f"🎯 Total real props scraped from {scraped_count} sportsbooks: {len(all_props)}")

        # If no real props found, check if we should use realistic fallback
        if not all_props:
            logger.info("🔄 No real props found, checking for today's games...")

            # REAL PROPS ONLY - NO FALLBACK GENERATION
            today_games = self._check_todays_games()
            if today_games:
                logger.info(f"📅 Found {len(today_games)} games today - REAL PROPS ONLY")
            else:
                logger.info("📅 No games scheduled today")

        # Remove duplicates
        unique_props = self._remove_duplicate_props(all_props)
        logger.info(f"🔧 After deduplication: {len(unique_props)} unique props")

        return unique_props

    def _remove_duplicate_props(self, props: List[PlayerProp]) -> List[PlayerProp]:
        """Smart duplicate removal - keep different lines from different sportsbooks"""
        # Group props by player and prop type
        prop_groups = {}

        for prop in props:
            key = (prop.player_name.lower().strip(), prop.prop_type.lower().strip())

            if key not in prop_groups:
                prop_groups[key] = []
            prop_groups[key].append(prop)

        unique_props = []

        for (player, prop_type), group_props in prop_groups.items():
            # For each player/prop_type combination, keep the best props

            # Sort by sportsbook quality (DraftKings, FanDuel first)
            sportsbook_priority = {
                'draftkings': 1, 'fanduel': 2, 'betmgm': 3, 'caesars': 4,
                'bet365': 5, 'espnbet': 6, 'pointsbet': 7, 'barstool': 8,
                'wynnbet': 9, 'unibet': 10
            }

            group_props.sort(key=lambda p: sportsbook_priority.get(p.sportsbook.lower(), 99))

            # Keep unique lines from different sportsbooks
            seen_lines = set()
            seen_sportsbooks = set()
            kept_for_this_group = []

            # First pass: prioritize different lines
            for prop in group_props:
                line_key = round(prop.line, 1)  # Round to avoid floating point issues

                if line_key not in seen_lines:
                    # Always keep different lines
                    seen_lines.add(line_key)
                    kept_for_this_group.append(prop)
                    seen_sportsbooks.add(prop.sportsbook.lower().strip())

            # Second pass: add more sportsbooks for same lines (up to limit)
            for prop in group_props:
                if prop in kept_for_this_group:
                    continue  # Already kept

                sportsbook_key = prop.sportsbook.lower().strip()

                if sportsbook_key not in seen_sportsbooks and len(kept_for_this_group) < 3:
                    # Keep different sportsbook for comparison
                    kept_for_this_group.append(prop)
                    seen_sportsbooks.add(sportsbook_key)

            # Add to final results
            unique_props.extend(kept_for_this_group)

        logger.info(f"🔧 Deduplication: {len(props)} → {len(unique_props)} props")

        # Log deduplication summary
        if len(props) > len(unique_props):
            removed = len(props) - len(unique_props)
            logger.info(f"🗑️ Removed {removed} duplicate props")

            # Show what was kept
            props_by_player = {}
            for prop in unique_props:
                if prop.player_name not in props_by_player:
                    props_by_player[prop.player_name] = []
                props_by_player[prop.player_name].append(prop)

            logger.info(f"👥 Final props for {len(props_by_player)} players:")
            for player, player_props in list(props_by_player.items())[:5]:
                prop_summary = {}
                for prop in player_props:
                    prop_type = prop.prop_type
                    if prop_type not in prop_summary:
                        prop_summary[prop_type] = []
                    prop_summary[prop_type].append(f"{prop.line}({prop.sportsbook})")

                summary_str = ", ".join([f"{pt}: {'/'.join(lines)}" for pt, lines in prop_summary.items()])
                logger.info(f"   • {player}: {summary_str}")

        return unique_props

    def _check_todays_games(self) -> List[Dict[str, Any]]:
        """Check if there are WNBA games today"""
        try:
            # Try to get today's games from our cache management system
            from supreme_cache_management import SupremeCacheManager
            cache_manager = SupremeCacheManager()
            games = cache_manager._get_todays_games()
            return games
        except:
            # Fallback: assume there might be games on typical game days
            from datetime import datetime
            today = datetime.now()
            # WNBA typically plays Tue, Thu, Fri, Sat, Sun
            if today.weekday() in [1, 3, 4, 5, 6]:  # Tue=1, Thu=3, Fri=4, Sat=5, Sun=6
                return [{'home_team': 'LAS', 'away_team': 'CON'}]  # Sample game
            return []

    def _generate_realistic_fallback_props(self, games: List[Dict[str, Any]]) -> List[PlayerProp]:
        """Generate realistic props based on actual player stats when real scraping fails"""
        props = []

        # Real WNBA player stats for realistic props
        player_stats = {
            "A'ja Wilson": {'points': 22.8, 'rebounds': 9.4, 'assists': 2.2},
            "Breanna Stewart": {'points': 23.1, 'rebounds': 9.3, 'assists': 3.8},
            "Caitlin Clark": {'points': 19.2, 'rebounds': 5.7, 'assists': 8.4},
            "Sabrina Ionescu": {'points': 18.2, 'rebounds': 4.4, 'assists': 6.2},
            "Kelsey Plum": {'points': 17.8, 'rebounds': 2.6, 'assists': 4.2}
        }

        sportsbooks = ['DraftKings', 'FanDuel', 'BetMGM']

        for game in games[:2]:  # Limit to 2 games
            # Get players likely to be in this game
            game_players = list(player_stats.keys())[:3]  # Top 3 players

            for player in game_players:
                stats = player_stats[player]

                for sportsbook in sportsbooks:
                    # Points prop
                    points_line = round(stats['points'] - 0.3, 1)  # Slightly under average
                    props.append(PlayerProp(
                        player_name=player,
                        prop_type='points',
                        line=points_line,
                        over_odds=-110,
                        under_odds=-110,
                        sportsbook=sportsbook,
                        game_info=f"Realistic fallback for today's games",
                        timestamp=datetime.now().isoformat()
                    ))

                    # Rebounds prop (for forwards/centers)
                    if stats['rebounds'] > 5:
                        rebounds_line = round(stats['rebounds'] - 0.5, 1)
                        props.append(PlayerProp(
                            player_name=player,
                            prop_type='rebounds',
                            line=rebounds_line,
                            over_odds=-110,
                            under_odds=-110,
                            sportsbook=sportsbook,
                            game_info=f"Realistic fallback for today's games",
                            timestamp=datetime.now().isoformat()
                        ))

                    # Assists prop (for guards)
                    if stats['assists'] > 3:
                        assists_line = round(stats['assists'] - 0.2, 1)
                        props.append(PlayerProp(
                            player_name=player,
                            prop_type='assists',
                            line=assists_line,
                            over_odds=-110,
                            under_odds=-110,
                            sportsbook=sportsbook,
                            game_info=f"Realistic fallback for today's games",
                            timestamp=datetime.now().isoformat()
                        ))

        logger.info(f"📊 Generated {len(props)} realistic fallback props")
        return props
    
    def save_props_to_db(self, props: List[PlayerProp]):
        """Save real props to database"""
        try:
            conn = sqlite3.connect('real_player_props.db')
            cursor = conn.cursor()
            
            # Create table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS real_player_props (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    player_name TEXT NOT NULL,
                    prop_type TEXT NOT NULL,
                    line REAL NOT NULL,
                    over_odds INTEGER NOT NULL,
                    under_odds INTEGER NOT NULL,
                    sportsbook TEXT NOT NULL,
                    game_info TEXT,
                    timestamp TEXT NOT NULL,
                    UNIQUE(player_name, prop_type, sportsbook, timestamp)
                )
            ''')
            
            # Insert props
            for prop in props:
                cursor.execute('''
                    INSERT OR REPLACE INTO real_player_props 
                    (player_name, prop_type, line, over_odds, under_odds, sportsbook, game_info, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    prop.player_name, prop.prop_type, prop.line,
                    prop.over_odds, prop.under_odds, prop.sportsbook,
                    prop.game_info, prop.timestamp
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"💾 Saved {len(props)} real props to database")
            
        except Exception as e:
            logger.error(f"❌ Database save error: {e}")

async def main():
    """Expert main function to test enhanced scraping"""
    scraper = RealPlayerPropsScraper()

    if not SELENIUM_AVAILABLE:
        print("❌ Selenium not available. Install with: pip install selenium webdriver-manager")
        return

    print("🎯 Starting EXPERT player props scraping with anti-detection...")
    print(f"📊 Available sportsbooks: {len(scraper.sportsbooks)}")
    print(f"🎭 User agents available: {len(scraper.user_agents)}")
    print(f"🔄 Proxies configured: {len(scraper.proxies) if scraper.proxies else 0}")

    # Show configured sportsbooks with their prop URLs
    for sportsbook_id, config in list(scraper.sportsbooks.items())[:5]:
        if 'prop_urls' in config:
            print(f"   • {config['name']}: {len(config['prop_urls'])} prop categories")
        else:
            print(f"   • {config['name']}: Generic scraping")

    # Expert scraping with enhanced methods (limit to 3 sportsbooks for testing)
    print("\n🚀 Starting expert scraping with human-like behavior...")
    real_props = await scraper.scrape_all_real_props(max_sportsbooks=3)

    if real_props:
        print(f"\n✅ Successfully scraped {len(real_props)} real player props!")

        # Group props by sportsbook
        props_by_book = {}
        for prop in real_props:
            if prop.sportsbook not in props_by_book:
                props_by_book[prop.sportsbook] = []
            props_by_book[prop.sportsbook].append(prop)

        print(f"\n📊 Props by sportsbook:")
        for sportsbook, props in props_by_book.items():
            print(f"   • {sportsbook}: {len(props)} props")

        # Show sample props
        print(f"\n🎯 Sample props:")
        for i, prop in enumerate(real_props[:10]):
            print(f"  {i+1}. {prop.player_name} {prop.prop_type} {prop.line} ({prop.sportsbook})")

        # Save to database
        scraper.save_props_to_db(real_props)

        print(f"\n💾 Saved all props to database: real_player_props.db")

    else:
        print("⚠️ No real props found. This could be due to:")
        print("   - No WNBA games today")
        print("   - Sportsbooks not offering props yet")
        print("   - Website structure changes")
        print("   - Anti-bot detection")
        print("   - Need to update selectors for specific sites")

    print(f"\n🎉 Scraping completed! Checked {len(scraper.sportsbooks)} sportsbook configurations.")

if __name__ == "__main__":
    asyncio.run(main())
