#!/usr/bin/env python3
"""
📅 DAILY MILITARY SCHEDULER
===========================

Automated scheduling system for military-grade WNBA data collection
Runs after 1 AM every day to collect fresh props and game data
"""

import schedule
import time
import subprocess
import logging
import os
import signal
import sys
from datetime import datetime, timedelta
from threading import Thread
import psutil

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('daily_military_scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DailyMilitaryScheduler:
    """📅 Daily Military-Grade Data Collection Scheduler"""
    
    def __init__(self):
        self.running_processes = {}
        self.scheduler_active = True
        self.last_run_time = None
        
    def kill_existing_processes(self):
        """🔪 Kill any existing military-grade processes"""
        try:
            # Kill processes by name
            process_names = [
                'consolidated_military_grade_live_system.py',
                'live_data_bridge.py',
                'enhanced_unified_dashboard_server.py'
            ]
            
            killed_count = 0
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    for process_name in process_names:
                        if process_name in cmdline:
                            logger.info(f"🔪 Killing existing process: {proc.info['pid']} - {process_name}")
                            proc.kill()
                            killed_count += 1
                            break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if killed_count > 0:
                logger.info(f"🔪 Killed {killed_count} existing processes")
                time.sleep(5)  # Wait for processes to fully terminate
            
        except Exception as e:
            logger.warning(f"⚠️ Error killing existing processes: {e}")
    
    def run_military_data_collection(self):
        """🎖️ Run the complete military-grade data collection system"""
        try:
            logger.info("🎖️ STARTING DAILY MILITARY-GRADE DATA COLLECTION")
            logger.info("=" * 60)
            
            # Kill any existing processes first
            self.kill_existing_processes()
            
            # Start live data bridge
            logger.info("🔗 Starting Live Data Bridge...")
            bridge_process = subprocess.Popen([
                sys.executable, 'live_data_bridge.py'
            ], cwd=os.getcwd())
            self.running_processes['bridge'] = bridge_process
            time.sleep(10)  # Wait for bridge to initialize
            
            # Start consolidated military-grade system
            logger.info("🎖️ Starting Consolidated Military-Grade Live System...")
            military_process = subprocess.Popen([
                sys.executable, 'consolidated_military_grade_live_system.py'
            ], cwd=os.getcwd())
            self.running_processes['military'] = military_process
            time.sleep(30)  # Wait for military system to initialize
            
            # Start enhanced dashboard server
            logger.info("📊 Starting Enhanced Unified Dashboard Server...")
            dashboard_process = subprocess.Popen([
                sys.executable, 'enhanced_unified_dashboard_server.py'
            ], cwd=os.getcwd())
            self.running_processes['dashboard'] = dashboard_process
            
            # Let the system run for data collection
            collection_duration = 3600  # 1 hour of data collection
            logger.info(f"⏱️ Running data collection for {collection_duration/60} minutes...")
            
            start_time = time.time()
            while time.time() - start_time < collection_duration:
                # Check if processes are still running
                for name, process in self.running_processes.items():
                    if process.poll() is not None:
                        logger.warning(f"⚠️ Process {name} has stopped, restarting...")
                        self.restart_process(name)
                
                time.sleep(60)  # Check every minute
            
            logger.info("✅ Daily data collection completed successfully")
            self.last_run_time = datetime.now()
            
            # Keep dashboard running, stop data collection
            if 'military' in self.running_processes:
                self.running_processes['military'].terminate()
                del self.running_processes['military']
            
            logger.info("📊 Dashboard server continues running for daily access")
            
        except Exception as e:
            logger.error(f"❌ Daily data collection failed: {e}")
            self.cleanup_processes()
    
    def restart_process(self, process_name):
        """🔄 Restart a specific process"""
        try:
            if process_name in self.running_processes:
                self.running_processes[process_name].terminate()
                time.sleep(5)
            
            if process_name == 'bridge':
                process = subprocess.Popen([sys.executable, 'live_data_bridge.py'], cwd=os.getcwd())
            elif process_name == 'military':
                process = subprocess.Popen([sys.executable, 'consolidated_military_grade_live_system.py'], cwd=os.getcwd())
            elif process_name == 'dashboard':
                process = subprocess.Popen([sys.executable, 'enhanced_unified_dashboard_server.py'], cwd=os.getcwd())
            else:
                return
            
            self.running_processes[process_name] = process
            logger.info(f"🔄 Restarted {process_name} process")
            
        except Exception as e:
            logger.error(f"❌ Failed to restart {process_name}: {e}")
    
    def cleanup_processes(self):
        """🧹 Clean up all running processes"""
        try:
            for name, process in self.running_processes.items():
                if process.poll() is None:
                    logger.info(f"🧹 Terminating {name} process...")
                    process.terminate()
                    time.sleep(2)
                    if process.poll() is None:
                        process.kill()
            
            self.running_processes.clear()
            logger.info("🧹 All processes cleaned up")
            
        except Exception as e:
            logger.error(f"❌ Cleanup error: {e}")
    
    def schedule_daily_collection(self):
        """📅 Schedule daily data collection after 1 AM"""
        # Schedule for 1:05 AM every day (5 minutes after 1 AM for safety)
        schedule.every().day.at("01:05").do(self.run_military_data_collection)
        
        logger.info("📅 Daily military-grade data collection scheduled for 1:05 AM")
        logger.info("🎖️ System will collect fresh props, games, and odds data daily")
        
        # Also schedule a weekly deep clean
        schedule.every().sunday.at("01:00").do(self.weekly_deep_clean)
        
        logger.info("🧹 Weekly deep clean scheduled for Sundays at 1:00 AM")
    
    def weekly_deep_clean(self):
        """🧹 Weekly deep database cleanup"""
        try:
            logger.info("🧹 Starting weekly deep clean...")
            
            # Clean old data from database
            import sqlite3
            conn = sqlite3.connect('military_grade_wnba_data.db')
            cursor = conn.cursor()
            
            # Remove props older than 7 days
            week_ago = (datetime.now() - timedelta(days=7)).isoformat()
            cursor.execute('DELETE FROM wnba_props WHERE timestamp < ?', (week_ago,))
            
            # Remove old games
            cursor.execute('DELETE FROM wnba_games WHERE timestamp < ?', (week_ago,))
            
            # Vacuum database
            cursor.execute('VACUUM')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Weekly deep clean completed")
            
        except Exception as e:
            logger.error(f"❌ Weekly deep clean failed: {e}")
    
    def run_scheduler(self):
        """🚀 Run the scheduler loop"""
        logger.info("🚀 DAILY MILITARY SCHEDULER STARTED")
        logger.info("📅 Next collection: Tomorrow at 1:05 AM")
        logger.info("🎖️ Military-grade WNBA data collection will run automatically")
        
        # Check if we should run immediately (for testing)
        current_hour = datetime.now().hour
        if current_hour >= 1 and current_hour < 6:  # Between 1 AM and 6 AM
            if self.last_run_time is None or (datetime.now() - self.last_run_time).days >= 1:
                logger.info("🎯 Running immediate collection (within collection window)")
                Thread(target=self.run_military_data_collection, daemon=True).start()
        
        try:
            while self.scheduler_active:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            logger.info("🛑 Scheduler stopped by user")
            self.cleanup_processes()
        except Exception as e:
            logger.error(f"❌ Scheduler error: {e}")
            self.cleanup_processes()
    
    def stop_scheduler(self):
        """🛑 Stop the scheduler"""
        self.scheduler_active = False
        self.cleanup_processes()

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info("🛑 Received shutdown signal")
    scheduler.stop_scheduler()
    sys.exit(0)

if __name__ == "__main__":
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and start scheduler
    scheduler = DailyMilitaryScheduler()
    scheduler.schedule_daily_collection()
    scheduler.run_scheduler()
