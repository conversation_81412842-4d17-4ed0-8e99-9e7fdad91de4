#!/usr/bin/env python3
"""
⚙️ PROXY CONFIGURATION HELPER
============================

Interactive helper to configure premium proxies for the scraper.
"""

import re
from pathlib import Path

def configure_smartproxy():
    """Configure SmartProxy credentials"""
    print("🏆 SMARTPROXY CONFIGURATION")
    print("-" * 30)
    print("1. Sign up at: https://smartproxy.com/")
    print("2. Choose 'Residential Proxies' plan")
    print("3. Get your credentials from dashboard")
    print()
    
    username = input("Enter your SmartProxy username: ").strip()
    password = input("Enter your SmartProxy password: ").strip()
    
    if username and password:
        return f'''
        # SMARTPROXY Configuration
        SMARTPROXY_USER = "{username}"
        SMARTPROXY_PASS = "{password}"
        proxies.extend([
            {{'http': f'http://{{SMARTPROXY_USER}}:{{SMARTPROXY_PASS}}@gate.smartproxy.com:7000', 
             'https': f'https://{{SMARTPROXY_USER}}:{{SMARTPROXY_PASS}}@gate.smartproxy.com:7000'}},
            {{'http': f'http://{{SMARTPROXY_USER}}:{{SMARTPROXY_PASS}}@gate.smartproxy.com:7001', 
             'https': f'https://{{SMARTPROXY_USER}}:{{SMARTPROXY_PASS}}@gate.smartproxy.com:7001'}},
        ])'''
    return None

def configure_oxylabs():
    """Configure Oxylabs credentials"""
    print("🏆 OXYLABS CONFIGURATION")
    print("-" * 25)
    print("1. Sign up at: https://oxylabs.io/")
    print("2. Choose datacenter or residential proxies")
    print("3. Get your credentials from dashboard")
    print()
    
    username = input("Enter your Oxylabs username: ").strip()
    password = input("Enter your Oxylabs password: ").strip()
    
    if username and password:
        return f'''
        # OXYLABS Configuration
        OXYLABS_USER = "{username}"
        OXYLABS_PASS = "{password}"
        proxies.extend([
            {{'http': f'http://{{OXYLABS_USER}}:{{OXYLABS_PASS}}@pr.oxylabs.io:7777', 
             'https': f'https://{{OXYLABS_USER}}:{{OXYLABS_PASS}}@pr.oxylabs.io:7777'}},
            {{'http': f'http://{{OXYLABS_USER}}:{{OXYLABS_PASS}}@dc.oxylabs.io:8001', 
             'https': f'https://{{OXYLABS_USER}}:{{OXYLABS_PASS}}@dc.oxylabs.io:8001'}},
        ])'''
    return None

def configure_brightdata():
    """Configure Bright Data credentials"""
    print("🏆 BRIGHT DATA CONFIGURATION")
    print("-" * 30)
    print("1. Sign up at: https://brightdata.com/")
    print("2. Create a proxy zone")
    print("3. Get your credentials and zone name")
    print()
    
    username = input("Enter your Bright Data username: ").strip()
    password = input("Enter your Bright Data password: ").strip()
    zone = input("Enter your zone name: ").strip()
    
    if username and password and zone:
        return f'''
        # BRIGHT DATA Configuration
        BRIGHT_USER = "{username}"
        BRIGHT_PASS = "{password}"
        BRIGHT_ZONE = "{zone}"
        proxies.extend([
            {{'http': f'http://{{BRIGHT_USER}}-zone-{{BRIGHT_ZONE}}:{{BRIGHT_PASS}}@zproxy.lum-superproxy.io:22225', 
             'https': f'https://{{BRIGHT_USER}}-zone-{{BRIGHT_ZONE}}:{{BRIGHT_PASS}}@zproxy.lum-superproxy.io:22225'}},
        ])'''
    return None

def update_scraper_file(proxy_config):
    """Update the scraper file with proxy configuration"""
    scraper_file = Path("real_player_props_scraper.py")
    
    if not scraper_file.exists():
        print("❌ real_player_props_scraper.py not found!")
        return False
    
    # Read current file
    with open(scraper_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the proxy configuration section
    pattern = r'(def _configure_premium_proxies\(self\).*?# FALLBACK: Free proxies)'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        # Insert the new configuration before the fallback
        old_section = match.group(1)
        new_section = old_section.replace(
            "# FALLBACK: Free proxies",
            proxy_config + "\n        \n        # FALLBACK: Free proxies"
        )
        
        # Replace in content
        content = content.replace(old_section, new_section)
        
        # Write back to file
        with open(scraper_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Proxy configuration updated successfully!")
        return True
    else:
        print("❌ Could not find proxy configuration section!")
        return False

def main():
    """Main configuration interface"""
    print("⚙️ PROXY CONFIGURATION HELPER")
    print("=" * 35)
    print()
    print("This helper will configure premium proxies for your WNBA scraper.")
    print("Choose a proxy service to configure:")
    print()
    print("1. SmartProxy (Recommended - $75/month)")
    print("2. Oxylabs (Premium - $300/month)")
    print("3. Bright Data (Enterprise - $500+/month)")
    print("4. Manual configuration")
    print("5. Exit")
    print()
    
    choice = input("Enter your choice (1-5): ").strip()
    
    proxy_config = None
    
    if choice == "1":
        proxy_config = configure_smartproxy()
    elif choice == "2":
        proxy_config = configure_oxylabs()
    elif choice == "3":
        proxy_config = configure_brightdata()
    elif choice == "4":
        print("\n📝 MANUAL CONFIGURATION")
        print("-" * 22)
        print("Edit real_player_props_scraper.py manually:")
        print("1. Find the _configure_premium_proxies method")
        print("2. Uncomment your preferred proxy service section")
        print("3. Add your credentials")
        print("4. Save the file")
        return
    elif choice == "5":
        print("👋 Goodbye!")
        return
    else:
        print("❌ Invalid choice!")
        return
    
    if proxy_config:
        print("\n📝 Generated configuration:")
        print(proxy_config)
        print()
        
        confirm = input("Apply this configuration? (y/n): ").strip().lower()
        if confirm == 'y':
            if update_scraper_file(proxy_config):
                print("\n🎯 Configuration complete!")
                print("\nNext steps:")
                print("1. Test your setup: python test_proxy_setup.py")
                print("2. Run scraper: python test_actual_scraping.py")
                print("3. Monitor performance: python monitor_scraper_performance.py")
            else:
                print("\n❌ Configuration failed!")
                print("Please configure manually using PREMIUM_PROXY_SETUP_GUIDE.md")
        else:
            print("Configuration cancelled.")
    else:
        print("❌ Configuration incomplete!")

if __name__ == "__main__":
    main()
