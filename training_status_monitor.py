#!/usr/bin/env python3
"""
🎯 TRAINING STATUS MONITOR
=========================

Monitor the progress of the consolidated real ML training system
and provide real-time status updates on all 46 WNBA models.
"""

import os
import sqlite3
import json
import time
from datetime import datetime
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TrainingStatusMonitor:
    """Monitor training progress and status"""
    
    def __init__(self):
        self.training_db = "consolidated_real_training.db"
        self.validation_db = "consolidated_validation.db"
        self.log_file = "consolidated_real_training_20250715.log"
        
        # 46 WNBA models to track
        self.expected_models = {
            'federated_models': [
                'federated_team_atl', 'federated_team_chi', 'federated_team_con', 'federated_team_dal',
                'federated_team_gsv', 'federated_team_ind', 'federated_team_las', 'federated_team_lv',
                'federated_team_min', 'federated_team_nyl', 'federated_team_pho', 'federated_team_sea', 'federated_team_was'
            ],
            'individual_models': [
                'PlayerPointsModel', 'HybridPlayerPointsModel', 'BayesianPlayerModel', 'FederatedPlayerModel',
                'PossessionBasedModel', 'LineupChemistryModel', 'CumulativeFatigueModel',
                'HighLeverageModel', 'TeamDynamicsModel', 'ContextualPerformanceModel',
                'InjuryImpactModel', 'CoachingStyleModel', 'ArenaEffectModel',
                'PlayerReboundsModel', 'PlayerAssistsModel', 'PlayerThreePointersModel', 'PlayerDoubleDoubleModel',
                'PreGameWinProbabilityModel', 'LiveWinProbabilityModel', 'UpsetPredictionModel',
                'enhanced_player_points_model', 'hybrid_gnn_model', 'multitask_model',
                'MetaModel', 'PlayerEmbeddingModel', 'RoleSpecificEnsemble', 'RoleClassifierModel', 'PlayerInteractionGNN',
                'GameTotalsModel', 'TeamScoringModel', 'ReboundPredictionModel', 'AssistPredictionModel', 'MinutesPredictionModel'
            ]
        }
        
        self.total_expected = len(self.expected_models['federated_models']) + len(self.expected_models['individual_models'])
    
    def check_training_status(self):
        """Check current training status"""
        status = {
            'timestamp': datetime.now().isoformat(),
            'total_expected_models': self.total_expected,
            'federated_models': self._check_federated_status(),
            'individual_models': self._check_individual_status(),
            'validation_results': self._check_validation_status(),
            'production_ready': self._check_production_ready(),
            'training_active': self._check_if_training_active()
        }
        
        return status
    
    def _check_federated_status(self):
        """Check federated learning status"""
        federated_status = {
            'expected': len(self.expected_models['federated_models']),
            'completed': 0,
            'in_progress': 0,
            'models': {}
        }
        
        try:
            if os.path.exists(self.training_db):
                conn = sqlite3.connect(self.training_db)
                cursor = conn.cursor()
                
                # Check for federated models in training results
                cursor.execute("SELECT model_id, training_status, final_mae, final_accuracy FROM training_results WHERE model_id LIKE 'federated_team_%'")
                results = cursor.fetchall()
                
                for model_id, status, mae, accuracy in results:
                    federated_status['models'][model_id] = {
                        'status': status,
                        'mae': mae,
                        'accuracy': accuracy
                    }
                    
                    if status == 'completed':
                        federated_status['completed'] += 1
                    elif status == 'training':
                        federated_status['in_progress'] += 1
                
                conn.close()
        except Exception as e:
            logger.debug(f"Database check error: {e}")
        
        return federated_status
    
    def _check_individual_status(self):
        """Check individual model training status"""
        individual_status = {
            'expected': len(self.expected_models['individual_models']),
            'completed': 0,
            'in_progress': 0,
            'models': {}
        }
        
        try:
            if os.path.exists(self.training_db):
                conn = sqlite3.connect(self.training_db)
                cursor = conn.cursor()
                
                # Check for individual models
                cursor.execute("SELECT model_id, training_status, final_mae, final_accuracy FROM training_results WHERE model_id NOT LIKE 'federated_team_%'")
                results = cursor.fetchall()
                
                for model_id, status, mae, accuracy in results:
                    individual_status['models'][model_id] = {
                        'status': status,
                        'mae': mae,
                        'accuracy': accuracy
                    }
                    
                    if status == 'completed':
                        individual_status['completed'] += 1
                    elif status == 'training':
                        individual_status['in_progress'] += 1
                
                conn.close()
        except Exception as e:
            logger.debug(f"Database check error: {e}")
        
        return individual_status
    
    def _check_validation_status(self):
        """Check validation status"""
        validation_status = {
            'validated_models': 0,
            'passed_validation': 0,
            'failed_validation': 0,
            'models': {}
        }
        
        try:
            if os.path.exists(self.validation_db):
                conn = sqlite3.connect(self.validation_db)
                cursor = conn.cursor()
                
                cursor.execute("SELECT model_id, validation_mae, validation_accuracy, production_ready FROM validation_results")
                results = cursor.fetchall()
                
                for model_id, mae, accuracy, production_ready in results:
                    validation_status['models'][model_id] = {
                        'mae': mae,
                        'accuracy': accuracy,
                        'production_ready': bool(production_ready)
                    }
                    
                    validation_status['validated_models'] += 1
                    if production_ready:
                        validation_status['passed_validation'] += 1
                    else:
                        validation_status['failed_validation'] += 1
                
                conn.close()
        except Exception as e:
            logger.debug(f"Validation database check error: {e}")
        
        return validation_status
    
    def _check_production_ready(self):
        """Check production-ready models"""
        production_ready = []
        
        try:
            if os.path.exists(self.validation_db):
                conn = sqlite3.connect(self.validation_db)
                cursor = conn.cursor()
                
                cursor.execute("SELECT model_id FROM validation_results WHERE production_ready = 1")
                results = cursor.fetchall()
                
                production_ready = [row[0] for row in results]
                conn.close()
        except Exception as e:
            logger.debug(f"Production ready check error: {e}")
        
        return production_ready
    
    def _check_if_training_active(self):
        """Check if training is currently active"""
        try:
            if os.path.exists(self.log_file):
                # Check if log file was modified recently (within last 5 minutes)
                mod_time = os.path.getmtime(self.log_file)
                current_time = time.time()
                return (current_time - mod_time) < 300  # 5 minutes
        except Exception as e:
            logger.debug(f"Training active check error: {e}")
        
        return False
    
    def print_status_report(self):
        """Print comprehensive status report"""
        status = self.check_training_status()
        
        print("🎯 CONSOLIDATED REAL ML TRAINING STATUS")
        print("=" * 60)
        print(f"📊 Total Expected Models: {status['total_expected_models']}")
        print(f"⏰ Last Check: {status['timestamp']}")
        print(f"🔄 Training Active: {'✅ YES' if status['training_active'] else '❌ NO'}")
        print()
        
        # Federated Models Status
        fed_status = status['federated_models']
        print(f"🌐 FEDERATED MODELS ({fed_status['expected']} total)")
        print(f"   ✅ Completed: {fed_status['completed']}")
        print(f"   🔄 In Progress: {fed_status['in_progress']}")
        print(f"   ⏳ Remaining: {fed_status['expected'] - fed_status['completed'] - fed_status['in_progress']}")
        
        # Individual Models Status
        ind_status = status['individual_models']
        print(f"🧠 INDIVIDUAL MODELS ({ind_status['expected']} total)")
        print(f"   ✅ Completed: {ind_status['completed']}")
        print(f"   🔄 In Progress: {ind_status['in_progress']}")
        print(f"   ⏳ Remaining: {ind_status['expected'] - ind_status['completed'] - ind_status['in_progress']}")
        
        # Validation Status
        val_status = status['validation_results']
        print(f"🔬 VALIDATION STATUS")
        print(f"   📊 Validated: {val_status['validated_models']}")
        print(f"   ✅ Passed: {val_status['passed_validation']}")
        print(f"   ❌ Failed: {val_status['failed_validation']}")
        
        # Production Ready
        prod_ready = status['production_ready']
        print(f"🚀 PRODUCTION READY: {len(prod_ready)} models")
        
        if prod_ready:
            print("   Production Models:")
            for model in prod_ready[:5]:  # Show first 5
                print(f"     + {model}")
            if len(prod_ready) > 5:
                print(f"     ... and {len(prod_ready) - 5} more")
        
        print()
        print("💡 Use 'python consolidated_real_ml_training_system.py' to start/continue training")

def main():
    """Main monitoring function"""
    monitor = TrainingStatusMonitor()
    monitor.print_status_report()

if __name__ == "__main__":
    main()
