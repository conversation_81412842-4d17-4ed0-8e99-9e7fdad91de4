#!/usr/bin/env python3
"""
🧪 ENHANCED SCRAPER TEST WITH MONITORING
=======================================

Comprehensive test of the enhanced prop scraper with detailed monitoring
of which strategies work best against blocking.
"""

import asyncio
import logging
import time
from datetime import datetime
from real_player_props_scraper import RealPlayerPropsScraper

# Enhanced logging configuration
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'scraper_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ScrapingMonitor:
    """Monitor scraping performance and strategy effectiveness"""
    
    def __init__(self):
        self.stats = {
            'total_attempts': 0,
            'successful_scrapes': 0,
            'blocked_attempts': 0,
            'chrome_success': 0,
            'firefox_success': 0,
            'requests_success': 0,
            'proxy_usage': 0,
            'strategies_used': [],
            'sportsbook_results': {},
            'start_time': time.time()
        }
    
    def log_attempt(self, sportsbook: str, strategy: str):
        """Log a scraping attempt"""
        self.stats['total_attempts'] += 1
        self.stats['strategies_used'].append(f"{sportsbook}_{strategy}")
        
        if sportsbook not in self.stats['sportsbook_results']:
            self.stats['sportsbook_results'][sportsbook] = {
                'attempts': 0, 'successes': 0, 'blocks': 0
            }
        
        self.stats['sportsbook_results'][sportsbook]['attempts'] += 1
    
    def log_success(self, sportsbook: str, strategy: str, props_count: int):
        """Log a successful scrape"""
        self.stats['successful_scrapes'] += 1

        if sportsbook not in self.stats['sportsbook_results']:
            self.stats['sportsbook_results'][sportsbook] = {
                'attempts': 0, 'successes': 0, 'blocks': 0
            }

        self.stats['sportsbook_results'][sportsbook]['successes'] += 1
        
        if 'chrome' in strategy.lower():
            self.stats['chrome_success'] += 1
        elif 'firefox' in strategy.lower():
            self.stats['firefox_success'] += 1
        elif 'requests' in strategy.lower():
            self.stats['requests_success'] += 1
        
        logger.info(f"✅ SUCCESS: {sportsbook} via {strategy} - {props_count} props")
    
    def log_block(self, sportsbook: str, strategy: str, reason: str):
        """Log a blocked attempt"""
        self.stats['blocked_attempts'] += 1
        self.stats['sportsbook_results'][sportsbook]['blocks'] += 1
        logger.warning(f"🚫 BLOCKED: {sportsbook} via {strategy} - {reason}")
    
    def log_proxy_usage(self):
        """Log proxy usage"""
        self.stats['proxy_usage'] += 1
    
    def print_summary(self):
        """Print comprehensive test summary"""
        duration = time.time() - self.stats['start_time']
        
        print("\n" + "="*60)
        print("🧪 ENHANCED SCRAPER TEST RESULTS")
        print("="*60)
        
        print(f"⏱️  Test Duration: {duration:.1f} seconds")
        print(f"🎯 Total Attempts: {self.stats['total_attempts']}")
        print(f"✅ Successful Scrapes: {self.stats['successful_scrapes']}")
        print(f"🚫 Blocked Attempts: {self.stats['blocked_attempts']}")
        print(f"📊 Success Rate: {(self.stats['successful_scrapes']/max(1,self.stats['total_attempts'])*100):.1f}%")
        
        print(f"\n🛠️  STRATEGY EFFECTIVENESS:")
        print(f"   • Chrome Success: {self.stats['chrome_success']}")
        print(f"   • Firefox Success: {self.stats['firefox_success']}")
        print(f"   • Requests Success: {self.stats['requests_success']}")
        print(f"   • Proxy Usage: {self.stats['proxy_usage']}")
        
        print(f"\n🏢 SPORTSBOOK RESULTS:")
        for sportsbook, results in self.stats['sportsbook_results'].items():
            success_rate = (results['successes']/max(1,results['attempts'])*100)
            print(f"   • {sportsbook}: {results['successes']}/{results['attempts']} ({success_rate:.1f}% success)")
        
        print(f"\n📈 STRATEGY USAGE:")
        strategy_counts = {}
        for strategy in self.stats['strategies_used']:
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
        
        for strategy, count in sorted(strategy_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"   • {strategy}: {count} times")

async def test_enhanced_scraper_with_monitoring():
    """Comprehensive test with detailed monitoring"""
    print("🧪 Starting Enhanced Scraper Test with Monitoring")
    print("="*55)
    
    monitor = ScrapingMonitor()
    scraper = RealPlayerPropsScraper()
    
    # Test 1: Proxy Testing
    print("\n🔍 Phase 1: Testing Proxy Connectivity")
    print("-" * 40)
    
    working_proxies = await scraper.test_proxies()
    if working_proxies:
        print(f"✅ {len(working_proxies)} working proxies found")
        monitor.log_proxy_usage()
    else:
        print("⚠️ No working proxies found - proceeding without proxies")
    
    # Test 2: Driver Creation Tests
    print("\n🚗 Phase 2: Testing Driver Creation")
    print("-" * 40)
    
    # Test Chrome driver
    print("Testing Chrome driver...")
    chrome_driver = scraper._create_driver()
    if chrome_driver:
        print("✅ Chrome driver created successfully")
        chrome_driver.quit()
        monitor.log_success("system", "chrome_driver", 0)
    else:
        print("❌ Chrome driver failed")
        monitor.log_block("system", "chrome_driver", "creation_failed")
    
    # Test Firefox driver
    print("Testing Firefox driver...")
    firefox_driver = scraper._create_firefox_driver()
    if firefox_driver:
        print("✅ Firefox driver created successfully")
        firefox_driver.quit()
        monitor.log_success("system", "firefox_driver", 0)
    else:
        print("❌ Firefox driver failed")
        monitor.log_block("system", "firefox_driver", "creation_failed")
    
    # Test 3: Individual Sportsbook Testing
    print("\n🎯 Phase 3: Testing Individual Sportsbooks")
    print("-" * 45)
    
    test_sportsbooks = ['draftkings', 'fanduel', 'betmgm']
    
    for sportsbook in test_sportsbooks:
        print(f"\n🏢 Testing {sportsbook.upper()}...")
        monitor.log_attempt(sportsbook, "selenium")
        
        try:
            if sportsbook == 'draftkings':
                props = await scraper.scrape_draftkings_props()
            elif sportsbook == 'fanduel':
                props = await scraper.scrape_fanduel_props()
            else:
                props = await scraper.scrape_generic_sportsbook(sportsbook)
            
            if props:
                monitor.log_success(sportsbook, "selenium", len(props))
                print(f"   📊 Sample props:")
                for i, prop in enumerate(props[:3]):
                    print(f"      {i+1}. {prop.player_name} {prop.prop_type} {prop.line}")
            else:
                print(f"   ℹ️ No props found (normal if no games scheduled)")
                monitor.log_success(sportsbook, "selenium", 0)
                
        except Exception as e:
            monitor.log_block(sportsbook, "selenium", str(e)[:50])
            print(f"   ❌ Error: {str(e)[:100]}...")
    
    # Test 4: Comprehensive Scraping Test
    print("\n🕷️ Phase 4: Comprehensive Multi-Sportsbook Test")
    print("-" * 50)
    
    try:
        all_props = await scraper.scrape_all_real_props(max_sportsbooks=3)
        
        if all_props:
            print(f"✅ Comprehensive test successful: {len(all_props)} total props")
            
            # Group by sportsbook
            by_sportsbook = {}
            for prop in all_props:
                if prop.sportsbook not in by_sportsbook:
                    by_sportsbook[prop.sportsbook] = []
                by_sportsbook[prop.sportsbook].append(prop)
            
            print(f"\n📊 Props by sportsbook:")
            for sportsbook, props in by_sportsbook.items():
                print(f"   • {sportsbook}: {len(props)} props")
                monitor.log_success(sportsbook, "comprehensive", len(props))
        else:
            print("ℹ️ No props found in comprehensive test")
            
    except Exception as e:
        print(f"❌ Comprehensive test failed: {e}")
        monitor.log_block("comprehensive", "all_methods", str(e)[:50])
    
    # Print final summary
    monitor.print_summary()
    
    # Recommendations based on results
    print(f"\n💡 RECOMMENDATIONS:")
    if monitor.stats['chrome_success'] > monitor.stats['firefox_success']:
        print("   • Chrome driver is more effective - prioritize Chrome")
    elif monitor.stats['firefox_success'] > 0:
        print("   • Firefox fallback is working - good backup strategy")
    
    if monitor.stats['proxy_usage'] > 0:
        print("   • Proxies are being used - consider premium proxy service")
    else:
        print("   • No proxy usage - consider adding working proxies")
    
    if monitor.stats['blocked_attempts'] > monitor.stats['successful_scrapes']:
        print("   • High blocking rate - increase delays and improve stealth")
    else:
        print("   • Good success rate - current strategies are effective")

if __name__ == "__main__":
    asyncio.run(test_enhanced_scraper_with_monitoring())
