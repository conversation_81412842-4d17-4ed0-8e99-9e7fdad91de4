#!/usr/bin/env python3
"""
🧪 CORE FUNCTIONALITY TEST
=========================

Simple test of core scraper functionality with monitoring.
"""

import asyncio
import logging
from datetime import datetime
from real_player_props_scraper import RealPlayerPropsScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'core_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

async def test_core_functionality():
    """Test core scraper functionality"""
    print("🧪 Testing Core Scraper Functionality")
    print("=" * 40)
    
    # Initialize scraper
    scraper = RealPlayerPropsScraper()
    
    print(f"✅ Scraper initialized")
    print(f"   • Sportsbooks: {len(scraper.sportsbooks)}")
    print(f"   • WNBA Players: {len(scraper.wnba_players)}")
    print(f"   • Prop Types: {len(scraper.prop_types)}")
    print(f"   • User Agents: {len(scraper.user_agents)}")
    print(f"   • Proxies: {len(scraper.proxies)}")
    
    # Test 1: Proxy Testing
    print(f"\n🔍 Testing Proxies...")
    working_proxies = await scraper.test_proxies()
    print(f"   Working proxies: {len(working_proxies)}/{len(scraper.proxies)}")
    
    # Test 2: Driver Creation
    print(f"\n🚗 Testing Driver Creation...")
    driver = scraper._create_driver()
    if driver:
        print("   ✅ Chrome driver created successfully")
        
        # Test basic navigation
        try:
            driver.get("https://httpbin.org/user-agent")
            user_agent = driver.find_element("tag name", "body").text
            print(f"   🎭 User agent working: {user_agent[:50]}...")
            driver.quit()
        except Exception as e:
            print(f"   ⚠️ Navigation test failed: {e}")
            driver.quit()
    else:
        print("   ❌ Chrome driver creation failed")
    
    # Test 3: DraftKings URLs
    print(f"\n🎯 Testing DraftKings Configuration...")
    dk_config = scraper.sportsbooks['draftkings']
    print(f"   Base URL: {dk_config['base_url']}")
    print(f"   Prop URLs:")
    for prop_type, url in dk_config['prop_urls'].items():
        print(f"      • {prop_type}: {url[:60]}...")
    
    # Test 4: bet365 URLs  
    print(f"\n🎯 Testing bet365 Configuration...")
    bet365_config = scraper.sportsbooks['bet365']
    print(f"   Base URL: {bet365_config['base_url']}")
    print(f"   Prop URLs:")
    for prop_type, url in bet365_config['prop_urls'].items():
        print(f"      • {prop_type}: {url[:60]}...")
    
    # Test 5: Player Name Matching
    print(f"\n👥 Testing Player Name Matching...")
    test_players = ["A'ja Wilson", "Caitlin Clark", "Breanna Stewart"]
    for player in test_players:
        if player in scraper.wnba_players:
            print(f"   ✅ {player} found in database")
        else:
            print(f"   ❌ {player} NOT found in database")
    
    # Test 6: Prop Type Validation
    print(f"\n📊 Testing Prop Type Validation...")
    test_props = [
        ("points", 25.5),
        ("rebounds", 8.5),
        ("assists", 6.5),
        ("threes", 3.5),
        ("minutes", 35.0)
    ]
    
    for prop_type, line in test_props:
        is_valid = scraper._validate_prop_line(prop_type, line)
        status = "✅" if is_valid else "❌"
        print(f"   {status} {prop_type} {line}: {'Valid' if is_valid else 'Invalid'}")
    
    # Test 7: Anti-Detection Features
    print(f"\n🛡️ Testing Anti-Detection Features...")
    
    # Test block detection
    test_content = [
        ("Normal content", "Welcome to DraftKings WNBA", False),
        ("Blocked content", "Access denied", True),
        ("Captcha", "Please verify you are human", True)
    ]
    
    # Create a mock driver for testing
    class MockDriver:
        def __init__(self, content, url="https://test.com", title="Test"):
            self._content = content
            self._url = url
            self._title = title

        @property
        def page_source(self):
            return self._content

        @property
        def current_url(self):
            return self._url

        @property
        def title(self):
            return self._title
    
    for test_name, content, should_block in test_content:
        mock_driver = MockDriver(content)
        is_blocked = scraper._check_for_captcha_or_blocks(mock_driver)
        status = "✅" if is_blocked == should_block else "❌"
        print(f"   {status} {test_name}: {'Blocked' if is_blocked else 'Allowed'}")
    
    # Test 8: Human-like Delays
    print(f"\n⏱️ Testing Human-like Delays...")
    delay_types = ['page_load', 'between_actions', 'between_sportsbooks']
    for delay_type in delay_types:
        min_delay, max_delay = scraper.human_delays.get(delay_type, (1, 3))
        print(f"   • {delay_type}: {min_delay}-{max_delay} seconds")
    
    print(f"\n🎯 Core functionality test completed!")
    print(f"\n💡 Next Steps:")
    print(f"   1. Run actual scraping test: python real_player_props_scraper.py")
    print(f"   2. Monitor logs for blocking patterns")
    print(f"   3. Add premium proxies if needed")
    print(f"   4. Adjust delays based on results")

if __name__ == "__main__":
    asyncio.run(test_core_functionality())
