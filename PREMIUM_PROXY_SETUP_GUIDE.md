# 🔄 Premium Proxy Setup Guide for WNBA Props Scraper

## 🏆 **Recommended Premium Proxy Services**

### **1. SMARTPROXY** ⭐⭐⭐⭐⭐ (BEST FOR SPORTS BETTING)
- **Why**: Excellent for DraftKings, FanDuel, bet365
- **Price**: $75/month for 5GB residential
- **Success Rate**: 85-95% for sports betting sites
- **Setup**: 
  ```python
  SMARTPROXY_USER = "your_username"
  SMARTPROXY_PASS = "your_password"
  ```
- **Sign up**: https://smartproxy.com/

### **2. OXYLABS** ⭐⭐⭐⭐⭐ (PREMIUM CHOICE)
- **Why**: High-quality datacenter + residential
- **Price**: $300/month for residential (enterprise)
- **Success Rate**: 90-98%
- **Setup**:
  ```python
  OXYLABS_USER = "your_username"
  OXYLABS_PASS = "your_password"
  ```
- **Sign up**: https://oxylabs.io/

### **3. BRIGHT DATA** ⭐⭐⭐⭐⭐ (HIGHEST SUCCESS)
- **Why**: Best success rate, used by Fortune 500
- **Price**: $500+/month (premium)
- **Success Rate**: 95-99%
- **Setup**:
  ```python
  BRIGHT_USER = "your_username"
  BRIGHT_PASS = "your_password"
  BRIGHT_ZONE = "your_zone"
  ```
- **Sign up**: https://brightdata.com/

### **4. PROXYMESH** ⭐⭐⭐⭐ (GOOD VALUE)
- **Why**: Rotating proxies, good for scraping
- **Price**: $20-100/month
- **Success Rate**: 70-85%
- **Setup**:
  ```python
  PROXYMESH_USER = "your_username"
  PROXYMESH_PASS = "your_password"
  ```
- **Sign up**: https://proxymesh.com/

### **5. STORM PROXIES** ⭐⭐⭐ (BUDGET OPTION)
- **Why**: Affordable, decent for testing
- **Price**: $50/month
- **Success Rate**: 60-75%
- **Setup**:
  ```python
  STORM_USER = "your_username"
  STORM_PASS = "your_password"
  ```
- **Sign up**: https://stormproxies.cn/

## 🛠️ **Setup Instructions**

### **Step 1: Choose a Proxy Service**
I recommend starting with **SMARTPROXY** for the best balance of price/performance for sports betting sites.

### **Step 2: Configure Credentials**
Edit `real_player_props_scraper.py` lines 347-412:

```python
# Example for SMARTPROXY
SMARTPROXY_USER = "sp12345678"  # Your username
SMARTPROXY_PASS = "your_password"  # Your password

# Uncomment these lines:
proxies.extend([
    {'http': f'http://{SMARTPROXY_USER}:{SMARTPROXY_PASS}@gate.smartproxy.com:7000', 
     'https': f'https://{SMARTPROXY_USER}:{SMARTPROXY_PASS}@gate.smartproxy.com:7000'},
    {'http': f'http://{SMARTPROXY_USER}:{SMARTPROXY_PASS}@gate.smartproxy.com:7001', 
     'https': f'https://{SMARTPROXY_USER}:{SMARTPROXY_PASS}@gate.smartproxy.com:7001'},
])
```

### **Step 3: Test Proxy Configuration**
```bash
python test_proxy_setup.py
```

### **Step 4: Run Enhanced Scraper**
```bash
python test_actual_scraping.py
```

## 🎯 **Quick Start with SMARTPROXY**

### **1. Sign Up**
- Go to https://smartproxy.com/
- Choose "Residential Proxies" plan
- Start with 5GB plan ($75/month)

### **2. Get Credentials**
- Login to dashboard
- Go to "Proxy Setup"
- Copy your username and password

### **3. Configure Scraper**
Replace lines in `real_player_props_scraper.py`:

```python
# Add your SMARTPROXY credentials
SMARTPROXY_USER = "your_actual_username"
SMARTPROXY_PASS = "your_actual_password"

# Uncomment the SMARTPROXY section (lines ~105-111)
proxies.extend([
    {'http': f'http://{SMARTPROXY_USER}:{SMARTPROXY_PASS}@gate.smartproxy.com:7000', 
     'https': f'https://{SMARTPROXY_USER}:{SMARTPROXY_PASS}@gate.smartproxy.com:7000'},
    {'http': f'http://{SMARTPROXY_USER}:{SMARTPROXY_PASS}@gate.smartproxy.com:7001', 
     'https': f'https://{SMARTPROXY_USER}:{SMARTPROXY_PASS}@gate.smartproxy.com:7001'},
])
```

## 📊 **Expected Results by Service**

| Service | Success Rate | DraftKings | bet365 | FanDuel | Price/Month |
|---------|-------------|------------|--------|---------|-------------|
| SMARTPROXY | 85-95% | ✅ Excellent | ✅ Excellent | ✅ Excellent | $75 |
| OXYLABS | 90-98% | ✅ Excellent | ✅ Excellent | ✅ Excellent | $300 |
| BRIGHT DATA | 95-99% | ✅ Perfect | ✅ Perfect | ✅ Perfect | $500+ |
| PROXYMESH | 70-85% | ✅ Good | ✅ Good | ✅ Good | $20-100 |
| STORM | 60-75% | ⚠️ Fair | ⚠️ Fair | ⚠️ Fair | $50 |
| Free Proxies | 5-20% | ❌ Poor | ❌ Poor | ❌ Poor | $0 |

## 🚀 **Testing Your Setup**

### **Test 1: Proxy Connectivity**
```bash
python -c "
from real_player_props_scraper import RealPlayerPropsScraper
import asyncio
scraper = RealPlayerPropsScraper()
asyncio.run(scraper.test_proxies())
"
```

### **Test 2: Actual Scraping**
```bash
python test_actual_scraping.py
```

### **Test 3: Monitor Performance**
```bash
python monitor_scraper_performance.py
```

## 💡 **Pro Tips**

1. **Start Small**: Begin with SMARTPROXY 5GB plan
2. **Monitor Usage**: Track bandwidth consumption
3. **Rotate Endpoints**: Use multiple proxy endpoints
4. **Test Timing**: Try different times of day
5. **Scale Up**: Upgrade if you need more volume

## 🎯 **Expected Transformation**

### **Before (Current Logs)**:
```
🚫 Detected blocking: blocked
🚫 Blocked on points, skipping...
🚫 Blocked on rebounds, skipping...
```

### **After (With Premium Proxies)**:
```
✅ DraftKings points: 15 props found
✅ DraftKings rebounds: 12 props found
✅ bet365 assists: 18 props found
🎯 Total: 45+ props successfully scraped
```

## 🔧 **Troubleshooting**

### **If proxies still fail:**
1. Check credentials are correct
2. Verify proxy endpoints
3. Try different proxy ports
4. Contact proxy service support
5. Test with curl first

### **If still getting blocked:**
1. Increase delays between requests
2. Try mobile user agents
3. Use residential proxies instead of datacenter
4. Rotate proxies more frequently

Your enhanced scraper + premium proxies = **90%+ success rate expected**!
