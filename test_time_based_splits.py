#!/usr/bin/env python3
"""
Test script for time-based data splitting
"""

import pandas as pd
import numpy as np
from pathlib import Path
from consolidated_real_ml_training_system import ConsolidatedRealMLTrainingSystem

def test_time_based_splits():
    print('Testing TIME-BASED data splitting for WNBA data...')
    
    try:
        system = ConsolidatedRealMLTrainingSystem()
        print('System initialized successfully')
        
        # Load real WNBA data to test time-based splitting
        data_paths = [
            Path("data/master/wnba_expert_dataset.csv"),
            Path("data/master/wnba_complete_10_year_dataset.csv"),  
            Path("data/master/wnba_consolidated_multi_target.csv"),
            Path("data/master/wnba_complete_dataset_2015_2025.csv"),
        ]
        
        data_path = None
        for path in data_paths:
            if path.exists():
                data_path = path
                break
        
        if not data_path:
            print('ERROR: No dataset found!')
            return
            
        print(f'Loading data from {data_path}...')
        # Load sample for testing
        df = pd.read_csv(data_path, nrows=10000, low_memory=False)
        print(f'Loaded {len(df):,} rows for testing')
        
        # Check available date columns
        date_columns = ['GAME_DATE', 'game_date', 'date', 'Date', 'season', 'SEASON', 'year', 'YEAR']
        available_date_cols = [col for col in date_columns if col in df.columns]
        print(f'Available date columns: {available_date_cols}')
        
        if available_date_cols:
            # Show sample date data
            for col in available_date_cols[:3]:  # Show first 3
                print(f'\nSample {col} values:')
                print(df[col].head(10).tolist())
                print(f'Unique values: {df[col].nunique()}')
        
        # Test time-based splitting
        print('\n' + '='*60)
        print('TESTING TIME-BASED SPLITTING:')
        
        if system._has_date_column(df):
            print('✅ Date column detected - testing time-based split...')
            
            train_indices, val_indices = system._get_time_based_split(df)
            
            print(f'\nSPLIT RESULTS:')
            print(f'  Train indices: {len(train_indices):,} samples')
            print(f'  Val indices: {len(val_indices):,} samples')
            print(f'  Total: {len(train_indices) + len(val_indices):,} samples')
            
            # Calculate split ratio
            total_samples = len(train_indices) + len(val_indices)
            train_ratio = len(train_indices) / total_samples * 100
            val_ratio = len(val_indices) / total_samples * 100
            
            print(f'  Split ratio: {train_ratio:.1f}% train, {val_ratio:.1f}% val')
            
            # Test with actual data loaders
            print('\nTesting data loader creation...')
            
            # Create dummy features and targets for testing
            features = np.random.randn(len(df), 50).astype(np.float32)
            targets = np.random.randn(len(df)).astype(np.float32)
            
            train_loader, val_loader = system._create_data_loaders(features, targets, df)
            
            print(f'✅ Data loaders created successfully!')
            print(f'  Train loader: {len(train_loader.dataset):,} samples')
            print(f'  Val loader: {len(val_loader.dataset):,} samples')
            
            # Verify no data leakage (train should be earlier than val)
            if available_date_cols:
                date_col = available_date_cols[0]
                train_sample_indices = train_indices[:100]  # Sample for checking
                val_sample_indices = val_indices[:100]
                
                if date_col in ['GAME_DATE', 'game_date', 'date', 'Date']:
                    try:
                        train_dates = pd.to_datetime(df.iloc[train_sample_indices][date_col], errors='coerce')
                        val_dates = pd.to_datetime(df.iloc[val_sample_indices][date_col], errors='coerce')
                        
                        train_max_date = train_dates.max()
                        val_min_date = val_dates.min()
                        
                        print(f'\nDATA LEAKAGE CHECK:')
                        print(f'  Latest train date: {train_max_date}')
                        print(f'  Earliest val date: {val_min_date}')
                        
                        if pd.notna(train_max_date) and pd.notna(val_min_date):
                            if train_max_date <= val_min_date:
                                print('  ✅ NO DATA LEAKAGE: Train dates are before validation dates')
                            else:
                                print('  ⚠️ POTENTIAL DATA LEAKAGE: Some train dates are after validation dates')
                    except Exception as e:
                        print(f'  ⚠️ Could not verify dates: {e}')
            
            print('\n🎉 SUCCESS: Time-based splitting is working correctly!')
            
        else:
            print('❌ No date columns found - would use chronological fallback')
            
    except Exception as e:
        print(f'ERROR: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_time_based_splits()
