#!/usr/bin/env python3
"""
🔍 PROXY SETUP TESTER
====================

Test your premium proxy configuration before running the scraper.
"""

import asyncio
import requests
import time
from datetime import datetime
from real_player_props_scraper import RealPlayerPropsScraper

def test_proxy_manually(proxy_config, test_url="http://httpbin.org/ip"):
    """Test a single proxy configuration manually"""
    try:
        print(f"   🧪 Testing: {proxy_config['http'][:50]}...")
        
        response = requests.get(
            test_url,
            proxies=proxy_config,
            timeout=10,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        
        if response.status_code == 200:
            ip_info = response.json()
            ip = ip_info.get('origin', 'Unknown')
            print(f"   ✅ SUCCESS - IP: {ip}")
            return True, ip
        else:
            print(f"   ❌ FAILED - Status: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"   ❌ ERROR - {str(e)[:50]}...")
        return False, None

def test_sportsbook_access(proxy_config, sportsbook_url):
    """Test if proxy can access a sportsbook"""
    try:
        print(f"   🎯 Testing sportsbook access...")
        
        response = requests.get(
            sportsbook_url,
            proxies=proxy_config,
            timeout=15,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        )
        
        if response.status_code == 200:
            content = response.text.lower()
            if 'blocked' in content or 'denied' in content or 'captcha' in content:
                print(f"   ⚠️ BLOCKED - Sportsbook detected proxy")
                return False
            else:
                print(f"   ✅ SUCCESS - Sportsbook accessible")
                return True
        else:
            print(f"   ❌ FAILED - Status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR - {str(e)[:50]}...")
        return False

async def comprehensive_proxy_test():
    """Comprehensive proxy testing"""
    print("🔍 COMPREHENSIVE PROXY SETUP TEST")
    print("=" * 45)
    
    # Initialize scraper
    scraper = RealPlayerPropsScraper()
    
    print(f"📊 Proxy Configuration:")
    print(f"   • Total proxies configured: {len(scraper.proxies)}")
    
    if not scraper.proxies:
        print("❌ No proxies configured!")
        print("\n💡 To configure proxies:")
        print("   1. Edit real_player_props_scraper.py")
        print("   2. Uncomment and configure premium proxy section")
        print("   3. Add your credentials")
        return
    
    # Test 1: Basic connectivity
    print(f"\n🧪 Test 1: Basic Proxy Connectivity")
    print("-" * 35)
    
    working_proxies = []
    proxy_ips = []
    
    for i, proxy in enumerate(scraper.proxies):
        print(f"\n🔄 Proxy {i+1}/{len(scraper.proxies)}:")
        success, ip = test_proxy_manually(proxy)
        if success:
            working_proxies.append(proxy)
            proxy_ips.append(ip)
    
    print(f"\n📊 Basic connectivity results:")
    print(f"   • Working proxies: {len(working_proxies)}/{len(scraper.proxies)}")
    print(f"   • Success rate: {(len(working_proxies)/len(scraper.proxies)*100):.1f}%")
    
    if proxy_ips:
        print(f"   • Proxy IPs: {', '.join(set(proxy_ips))}")
    
    if not working_proxies:
        print("❌ No working proxies found!")
        print("\n🔧 Troubleshooting:")
        print("   • Check proxy credentials")
        print("   • Verify proxy endpoints")
        print("   • Contact proxy service support")
        return
    
    # Test 2: Sportsbook access
    print(f"\n🎯 Test 2: Sportsbook Access")
    print("-" * 30)
    
    test_urls = [
        ("DraftKings", "https://sportsbook.draftkings.com/"),
        ("FanDuel", "https://sportsbook.fanduel.com/"),
        ("bet365", "https://www.bet365.com/"),
    ]
    
    sportsbook_results = {}
    
    for sportsbook_name, url in test_urls:
        print(f"\n🏢 Testing {sportsbook_name}:")
        
        success_count = 0
        for i, proxy in enumerate(working_proxies[:3]):  # Test first 3 working proxies
            print(f"   Proxy {i+1}:")
            if test_sportsbook_access(proxy, url):
                success_count += 1
        
        sportsbook_results[sportsbook_name] = success_count
        print(f"   📊 {sportsbook_name}: {success_count}/{min(3, len(working_proxies))} proxies successful")
    
    # Test 3: Scraper integration
    print(f"\n🕷️ Test 3: Scraper Integration")
    print("-" * 32)
    
    print("Testing scraper proxy integration...")
    scraper_working = await scraper.test_proxies()
    print(f"   • Scraper detected: {len(scraper_working)} working proxies")
    
    # Test 4: Performance test
    print(f"\n⚡ Test 4: Performance Test")
    print("-" * 28)
    
    if working_proxies:
        print("Testing proxy speed...")
        speeds = []
        
        for i, proxy in enumerate(working_proxies[:3]):
            start_time = time.time()
            success, _ = test_proxy_manually(proxy, "http://httpbin.org/delay/1")
            end_time = time.time()
            
            if success:
                speed = end_time - start_time
                speeds.append(speed)
                print(f"   • Proxy {i+1}: {speed:.2f} seconds")
        
        if speeds:
            avg_speed = sum(speeds) / len(speeds)
            print(f"   📊 Average response time: {avg_speed:.2f} seconds")
    
    # Final summary and recommendations
    print(f"\n" + "="*50)
    print(f"📋 PROXY SETUP TEST SUMMARY")
    print(f"="*50)
    
    if len(working_proxies) >= 3:
        print(f"✅ EXCELLENT: {len(working_proxies)} working proxies")
        print(f"🎯 Your setup is ready for production scraping!")
    elif len(working_proxies) >= 1:
        print(f"⚠️ GOOD: {len(working_proxies)} working proxies")
        print(f"💡 Consider adding more proxies for better reliability")
    else:
        print(f"❌ POOR: No working proxies")
        print(f"🔧 Fix proxy configuration before scraping")
    
    print(f"\n🏢 Sportsbook Access Results:")
    for sportsbook, success_count in sportsbook_results.items():
        if success_count > 0:
            print(f"   ✅ {sportsbook}: {success_count} proxies working")
        else:
            print(f"   ❌ {sportsbook}: No proxies working")
    
    print(f"\n💡 Recommendations:")
    if len(working_proxies) >= 3:
        print(f"   • Run actual scraping test: python test_actual_scraping.py")
        print(f"   • Monitor performance: python monitor_scraper_performance.py")
    else:
        print(f"   • Add more premium proxies")
        print(f"   • Check proxy service documentation")
        print(f"   • Contact proxy service support")
    
    print(f"\n📄 Next steps:")
    print(f"   1. If proxies working: Run python test_actual_scraping.py")
    print(f"   2. If issues: Check PREMIUM_PROXY_SETUP_GUIDE.md")
    print(f"   3. Monitor: python monitor_scraper_performance.py")

if __name__ == "__main__":
    asyncio.run(comprehensive_proxy_test())
